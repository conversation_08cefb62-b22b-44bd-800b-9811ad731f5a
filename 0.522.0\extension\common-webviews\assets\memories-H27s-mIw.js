import{l as Be,f as Oe,a as De,t as b,b as n,A as fe,B as ie,ak as Ie,w as ee,_ as me,C as g,a4 as V,m as h,L as e,P as pe,a0 as He,D as he,H as Me,I as ne,J as te,N as $e,a3 as Se,F as Q,am as Re,Y as q,T as Le,Z as be,G as j,M as A,Q as se,a5 as Pe,X as Te,a6 as _e,S as je,al as Ue,az as Ve}from"./SpinnerAugment-B-W1rkU5.js";import"./design-system-init-CO3OATOl.js";import{h as We,c as W,W as ye,e as qe,i as Qe}from"./IconButtonAugment-Cdot7Te3.js";import{O as Xe}from"./OpenFileButton-DyxQXXwJ.js";import{S as Ye}from"./TextAreaAugment-DEiWzBPO.js";import{C as Ze}from"./check-C7whjN2f.js";import{C as Ee,E as ke,D as U,R as Je,M as ge,f as Ke,g as et,h as tt}from"./index-BZ1aQDkr.js";import{M as we}from"./message-broker-DiyMl4p8.js";import{M as st}from"./MarkdownEditor-BtN9HMBx.js";import{B as Fe}from"./ButtonAugment-BsoJM5iW.js";import{C as Ge}from"./chevron-down-BvquqDa7.js";import{F as ot}from"./Filespan-CTglRHBI.js";import{T as ze,a as re}from"./CardAugment-6M90JowR.js";import"./chat-model-context-xODWxtdR.js";import"./index-C4gKbsWy.js";import"./index-Bmm_dv1C.js";import"./remote-agents-client-DbZSIpd6.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-BYdL3GOo.js";import"./BaseTextInput-NmI9DKfY.js";import"./async-messaging-h5fbTmxI.js";import"./focusTrapStack-wx6NNrdM.js";import"./isObjectLike-KnT2wtGt.js";var at=Oe("<svg><!></svg>");function xe(X,I){const $=Be(I,["children","$$slots","$$events","$$legacy"]);var S=at();De(S,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...$}));var i=b(S);We(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',!0),n(X,S)}var nt=Q("<!> <!>",1),rt=Q('<div class="rules-dropdown-content svelte-18wohv"><!> <!></div>'),it=Q("<!> <!>",1);function lt(X,I){fe(I,!1);const[$,S]=Se(),i=()=>V(s,"$rulesFiles",$),B=()=>V(L,"$selectedRule",$),y=()=>V(e(F),"$focusedIndex",$),R=h(),Y=h(),Z=h();let oe=ie(I,"onRuleSelected",8),ae=ie(I,"disabled",8,!1);const le=new we(W),ce=new Ee,l=new ke(W,le,ce),s=ee([]),w=ee(!0),L=ee(void 0);let F=h(void 0),O=h(()=>{});Ie(()=>{(async function(){try{w.set(!0);const u=await l.findRules("",100);s.set(u)}catch(u){console.error("Failed to load rules:",u),s.set([])}finally{w.set(!1)}})();const d=u=>{var T;((T=u.data)==null?void 0:T.type)===ye.getRulesListResponse&&(s.set(u.data.data||[]),w.set(!1))};return window.addEventListener("message",d),()=>{window.removeEventListener("message",d)}});let D=h(),x=h(!1);function de(d){g(x,d)}me(()=>i(),()=>{g(R,i().length>0)}),me(()=>(pe(ae()),e(R)),()=>{g(Y,ae()||!e(R))}),me(()=>e(R),()=>{g(Z,e(R)?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")}),He(),he();var c=Me(),J=ne(c),K=d=>{var u=Me(),T=ne(u),ue=v=>{U.Root(v,{onOpenChange:de,get requestClose(){return e(O)},set requestClose(a){g(O,a)},get focusedIndex(){return e(F)},set focusedIndex(a){Pe(g(F,a),"$focusedIndex",$)},children:(a,m)=>{var f=it(),p=ne(f);U.Trigger(p,{children:(E,ve)=>{const P=A(()=>(pe(re),j(()=>[re.Hover]))),_=A(()=>!e(x)&&void 0);Re(ze(E,{get content(){return e(Z)},get triggerOn(){return e(P)},side:"top",get open(){return e(_)},children:(k,C)=>{Fe(k,{color:"neutral",variant:"soft",size:1,get disabled(){return e(Y)},children:(r,o)=>{var M=q();Le(()=>be(M,(B(),j(()=>B()?B().path:"Rules")))),n(r,M)},$$slots:{default:!0,iconLeft:(r,o)=>{xe(r,{slot:"iconLeft"})},iconRight:(r,o)=>{Ge(r,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),k=>g(D,k),()=>e(D))},$$slots:{default:!0}});var H=se(p,2);U.Content(H,{side:"bottom",align:"start",children:(E,ve)=>{var P=rt(),_=b(P);qe(_,1,i,Qe,(r,o,M)=>{const N=A(()=>y()===M);U.Item(r,{onSelect:()=>function(G){L.set(G),oe()(G),e(O)()}(e(o)),get highlight(){return e(N)},children:(G,z)=>{ot(G,{get filepath(){return e(o),j(()=>e(o).path)}})},$$slots:{default:!0}})});var k=se(_,2),C=r=>{var o=nt(),M=ne(o);U.Separator(M,{});var N=se(M,2);U.Label(N,{children:(G,z)=>{Te(G,{size:1,color:"neutral",children:(Ne,vt)=>{var Ce=q();Le(Ae=>be(Ce,Ae),[()=>(i(),y(),j(()=>`Move to ${i()[y()].path}`))],A),n(Ne,Ce)},$$slots:{default:!0}})},$$slots:{default:!0}}),n(r,o)};te(k,r=>{y(),i(),j(()=>y()!==void 0&&i()[y()])&&r(C)}),n(E,P)},$$slots:{default:!0}}),n(a,f)},$$slots:{default:!0},$$legacy:!0})},t=v=>{const a=A(()=>(pe(re),j(()=>[re.Hover])));Re(ze(v,{get content(){return e(Z)},get triggerOn(){return e(a)},side:"top",children:(m,f)=>{Fe(m,{color:"neutral",variant:"soft",size:1,disabled:!0,children:(p,H)=>{var E=q("Rules");n(p,E)},$$slots:{default:!0,iconLeft:(p,H)=>{xe(p,{slot:"iconLeft"})},iconRight:(p,H)=>{Ge(p,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),m=>g(D,m),()=>e(D))};te(T,v=>{e(R)?v(ue):v(t,!1)}),n(d,u)};te(J,d=>{V(w,"$loading",$)||d(K)}),n(X,c),$e(),S()}var ct=Q('<div slot="iconLeft" class="c-move-text-btn__left_icon svelte-1yddhs6"><!></div>'),dt=Q('<div class="l-file-controls svelte-1yddhs6" slot="header"><div class="l-file-controls-left svelte-1yddhs6"><div class="c-move-text-btn svelte-1yddhs6"><!></div> <div class="c-move-text-btn svelte-1yddhs6"><!></div></div> <div class="l-file-controls-right svelte-1yddhs6"><!></div></div>'),ut=Q('<div class="c-memories-container svelte-1vchs21"><!></div>');Ve(function(X,I){fe(I,!1);const[$,S]=Se(),i=()=>V(R,"$editorContent",$),B=()=>V(Y,"$editorPath",$),y=new we(W),R=ee(null),Y=ee(null),Z={handleMessageFromExtension(l){const s=l.data;if(s&&s.type===ye.loadFile){if(s.data.content!==void 0){const w=s.data.content.replace(/^\n+/,"");R.set(w)}s.data.pathName&&Y.set(s.data.pathName)}return!0}};Ie(()=>{y.registerConsumer(Z),W.postMessage({type:ye.memoriesLoaded})}),_e(()=>{y.dispose()}),he();var oe=ut();je("message",Ue,function(...l){var s;(s=y.onMessageFromExtension)==null||s.apply(this,l)});var ae=b(oe),le=l=>{(function(s,w){fe(w,!1);let L=ie(w,"text",12),F=ie(w,"path",8);const O=new we(W),D=new Ee,x=new ke(W,O,D),de=new Je(O);let c=h(""),J=h(0),K=h(0),d=h("neutral");const u=async()=>{F()&&x.saveFile({repoRoot:"",pathName:F(),content:L()})};async function T(t){if(!e(c))return;let v,a,m;const f=e(c).slice(0,20);if(t==="userGuidelines"?(v="Move Content to User Guidelines",a=`Are you sure you want to move the selected content "${f}" to your user guidelines?`,m=ge.userGuidelines):t==="augmentGuidelines"?(v="Move Content to Workspace Guidelines",a=`Are you sure you want to move the selected content "${f}" to workspace guidelines?`,m=ge.augmentGuidelines):(v="Move Content to Rule",a=`Are you sure you want to move the selected content "${f}" to rule file "${t.rule.path}"?`,m=ge.rules),!await x.openConfirmationModal({title:v,message:a,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;t==="userGuidelines"?x.updateUserGuidelines(e(c)):t==="augmentGuidelines"?x.updateWorkspaceGuidelines(e(c)):(await de.updateRuleContent({type:t.rule.type,path:t.rule.path,content:t.rule.content+`

`+e(c),description:t.rule.description}),x.showNotification({message:`Moved content "${f}" to rule file "${t.rule.path}"`,type:"info",openFileMessage:{repoRoot:"",pathName:`${et}/${tt}/${t.rule.path}`}}));const p=L().substring(0,e(J))+L().substring(e(K));return L(p),await u(),x.reportAgentSessionEvent({eventName:Ke.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:m}}}),"success"}async function ue(t){await T({rule:t})}he(),st(s,{saveFunction:u,variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get selectedText(){return e(c)},set selectedText(t){g(c,t)},get selectionStart(){return e(J)},set selectionStart(t){g(J,t)},get selectionEnd(){return e(K)},set selectionEnd(t){g(K,t)},get value(){return L()},set value(t){L(t)},$$slots:{header:(t,v)=>{var a=dt(),m=b(a),f=b(m),p=b(f);const H=A(()=>!e(c));Ye(p,{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:()=>T("userGuidelines"),get disabled(){return e(H)},stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,get state(){return e(d)},set state(C){g(d,C)},children:(C,r)=>{var o=q("User Guidelines");n(C,o)},$$slots:{default:!0,iconLeft:(C,r)=>{var o=ct(),M=b(o),N=z=>{Ze(z,{})},G=z=>{xe(z,{})};te(M,z=>{e(d)==="success"?z(N):z(G,!1)}),n(C,o)}},$$legacy:!0});var E=se(f,2),ve=b(E);const P=A(()=>!e(c));lt(ve,{onRuleSelected:ue,get disabled(){return e(P)}});var _=se(m,2),k=b(_);Xe(k,{size:1,get path(){return F()},variant:"soft",onOpenLocalFile:async()=>(x.openFile({repoRoot:"",pathName:F()}),"success"),$$slots:{text:(C,r)=>{Te(C,{slot:"text",size:1,children:(o,M)=>{var N=q("Augment-Memories.md");n(o,N)},$$slots:{default:!0}})}}}),n(t,a)}},$$legacy:!0}),$e()})(l,{get text(){return i()},get path(){return B()}})},ce=l=>{var s=q("Loading memories...");n(l,s)};te(ae,l=>{i()!==null&&B()!==null?l(le):l(ce,!1)}),n(X,oe),$e(),S()},{target:document.getElementById("app")});
