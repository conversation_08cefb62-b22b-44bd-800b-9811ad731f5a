<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Next Edit Suggestions</title>
    <script nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <script type="module" crossorigin src="./assets/next-edit-suggestions-DAHVa5ZD.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-B-W1rkU5.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-CO3OATOl.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/await-BLwytcxI.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/index-C0pD_8h5.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-DrXxAvzA.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/index-Bmm_dv1C.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BPaFHx0a.js" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
    <link rel="stylesheet" crossorigin href="./assets/next-edit-suggestions-Df4-uiQ1.css" nonce="nonce-JyU+9mzt9o8d1x39tFtCuA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
