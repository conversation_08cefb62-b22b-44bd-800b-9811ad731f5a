var ya=Object.defineProperty;var _a=(r,e,t)=>e in r?ya(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var Ie=(r,e,t)=>_a(r,typeof e!="symbol"?e+"":e,t);import{f as $t,b as d,w as ut,aD as Rr,aA as mr,A as Xe,B as w,D as tt,F as g,J as W,L as n,G as y,t as h,Q as p,M as $e,T as ge,ab as bt,Z as Ae,aF as ls,X as le,Y as Z,S as ft,N as et,_ as _e,C as m,m as G,P as _,a0 as ct,V as Qt,W as wa,H as it,I as Ce,a4 as st,a3 as Nt,z as Ca,l as us,$ as ba,a as Ds,a2 as $a,K as Ct,al as kr,a7 as Sa,b4 as Er,ay as Ks,v as _s,u as ds,b5 as Ir,aE as lr,a5 as ks,O as xr,ak as ka,a$ as zr,a6 as Kr,b6 as xa,az as Ma}from"./SpinnerAugment-B-W1rkU5.js";import"./design-system-init-CO3OATOl.js";import{W as Ge,d as wt,e as dt,I as ts,b as Aa,c as lt,i as qt,a as Ta,h as nr,f as Lr,g as Na,H as dr}from"./IconButtonAugment-Cdot7Te3.js";import{R as Or,M as Ra}from"./message-broker-DiyMl4p8.js";import{G as Ea,S as Ia,b as za,N as La,L as Oa,c as ht,M as ns,D as Za,F as Pa,f as Yr,R as Fa,d as Zr,T as Qr,e as ja,C as Da,P as cr,g as Va,h as Ua,i as qa,A as Ha,j as Ba,k as Ga,U as Pr}from"./user-vtiOgHx6.js";import{Q as ur,a2 as Zt,O as Be,i as hr,t as gr,T as Xt,D as Ye,a3 as Ja,C as Xr,E as ea,a4 as Hs,f as pr,A as Fr,g as Wa,h as Ka,R as Ya}from"./index-BZ1aQDkr.js";import{G as Qa,D as Bs,C as Xa,P as hs,B as Mr,T as fr,a as ta,S as yr,c as en}from"./download-WeyF9LzE.js";import{o as Js}from"./keypress-DD1aQVr0.js";import{V as sa}from"./VSCodeCodicon-PwzMb_dE.js";import{A as tn}from"./async-messaging-h5fbTmxI.js";import{c as _r}from"./svelte-component-BFVtr7-z.js";import{k as sn,C as ra,a as rn,T as Ws}from"./CollapseButtonAugment-FMX0ZXJp.js";import{D as an}from"./Drawer-DF8SmKU6.js";import{b as aa,T as Ft,a as ps,p as nn}from"./CardAugment-6M90JowR.js";import{B as Qe}from"./ButtonAugment-BsoJM5iW.js";import{C as xs}from"./CalloutAugment-BjqqGsdn.js";import{E as on}from"./ellipsis-tqj1KnpB.js";import{P as ln}from"./pen-to-square-CZY7t64W.js";import{T as na,S as dn}from"./TextAreaAugment-DEiWzBPO.js";import{C as cn}from"./copy-C63CnUj2.js";import{C as Ar}from"./chevron-down-BvquqDa7.js";import{M as un}from"./index-BPaFHx0a.js";import{M as hn}from"./MarkdownEditor-BtN9HMBx.js";import{R as pn}from"./RulesModeSelector-CLcZ4jbN.js";import{M as ia}from"./ModalAugment-DU_D4O_Y.js";import"./types-CGlLNakm.js";import"./focusTrapStack-wx6NNrdM.js";import"./isObjectLike-KnT2wtGt.js";import"./BaseTextInput-NmI9DKfY.js";import"./index-C0pD_8h5.js";import"./index-Bmm_dv1C.js";const ws={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class vn{constructor(e,t=ws){Ie(this,"timerId",null);Ie(this,"currentMS");Ie(this,"step",0);Ie(this,"params");this.callback=e;const s={...t};s.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),s.maxMS=ws.maxMS),s.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),s.initialMS=ws.initialMS),s.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),s.mult=ws.mult),s.maxSteps!==void 0&&s.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),s.maxSteps=ws.maxSteps),this.params=s,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}var mn=$t('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z" fill="currentColor"></path><path d="M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z" fill="currentColor"></path></svg>');function gn(r){var e=mn();d(r,e)}var fn=$t('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z" fill="currentColor"></path></svg>');function yn(r){var e=fn();d(r,e)}var _n=$t('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>');function Vt(r){var e=_n();d(r,e)}var Pe,wr,wn=$t('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z" fill="currentColor"></path></svg>');function Cn(r){var e=wn();d(r,e)}class Ms{constructor(e){Ie(this,"configs",ut([]));Ie(this,"pollingManager");Ie(this,"_enableDebugFeatures",ut(!1));Ie(this,"_settingsComponentSupported",ut({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));Ie(this,"_enableAgentMode",ut(!1));Ie(this,"_enableAgentSwarmMode",ut(!1));Ie(this,"_enableNativeRemoteMcp",ut(!0));Ie(this,"_hasEverUsedRemoteAgent",ut(!1));Ie(this,"_enableInitialOrientation",ut(!1));Ie(this,"_userTier",ut("unknown"));Ie(this,"_guidelines",ut({}));this._host=e,this.pollingManager=new vn(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,s=e.oauthUrl;if(e.identifier.hostName===ur.remoteToolHost){let a=e.identifier.toolId;switch(typeof a=="string"&&/^\d+$/.test(a)&&(a=Number(a)),a){case Zt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:Qa,requiresAuthentication:t,authUrl:s};case Zt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:Oa,requiresAuthentication:t,authUrl:s};case Zt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:yn,requiresAuthentication:t,authUrl:s};case Zt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:La,requiresAuthentication:t,authUrl:s};case Zt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:gn,requiresAuthentication:t,authUrl:s};case Zt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:za,requiresAuthentication:t,authUrl:s};case Zt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:Ia,requiresAuthentication:t,authUrl:s};case Zt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:Ea,requiresAuthentication:t,authUrl:s};case Zt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled RemoteToolId: ${a}`)}}else if(e.identifier.hostName===ur.localToolHost){const a=e.identifier.toolId;switch(a){case Be.readFile:case Be.editFile:case Be.saveFile:case Be.launchProcess:case Be.killProcess:case Be.readProcess:case Be.writeProcess:case Be.listProcesses:case Be.waitProcess:case Be.openBrowser:case Be.clarify:case Be.onboardingSubAgent:case Be.strReplaceEditor:case Be.remember:case Be.diagnostics:case Be.setupScript:case Be.readTerminal:case Be.gitCommitRetrieval:case Be.memoryRetrieval:case Be.startWorkerAgent:case Be.readWorkerState:case Be.waitForWorkerAgent:case Be.sendInstructionToWorkerAgent:case Be.stopWorkerAgent:case Be.deleteWorkerAgent:case Be.readWorkerAgentEdits:case Be.applyWorkerAgentEdits:case Be.LocalSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:Vt,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled LocalToolType: ${a}`)}}else if(e.identifier.hostName===ur.sidecarToolHost){const a=e.identifier.toolId;switch(a){case ht.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:ns,requiresAuthentication:t,authUrl:s};case ht.shell:return{displayName:"Shell",description:"Shell",icon:ns,requiresAuthentication:t,authUrl:s};case ht.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:ns,requiresAuthentication:t,authUrl:s};case ht.view:return{displayName:"File View",description:"File Viewer",icon:ns,requiresAuthentication:t,authUrl:s};case ht.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:ns,requiresAuthentication:t,authUrl:s};case ht.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Cn,requiresAuthentication:t,authUrl:s};case ht.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:Vt,requiresAuthentication:t,authUrl:s};case ht.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Pa,requiresAuthentication:t,authUrl:s};case ht.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:Vt,requiresAuthentication:t,authUrl:s};case ht.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:Vt,requiresAuthentication:t,authUrl:s};case ht.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:Vt,requiresAuthentication:t,authUrl:s};case ht.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:Vt,requiresAuthentication:t,authUrl:s};case ht.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:Vt,requiresAuthentication:t,authUrl:s};case ht.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:Vt,requiresAuthentication:t,authUrl:s};case ht.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Za,requiresAuthentication:t,authUrl:s};case ht.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:ns,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled SidecarToolType: ${a}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:s}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case Ge.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(t.data.enableAgentSwarmMode),t.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(t.data.hasEverUsedRemoteAgent),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),t.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(t.data.enableNativeRemoteMcp),!0;case Ge.toolConfigDefinitionsResponse:return this.configs.update(s=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(a=>{const l=s.find(i=>i.name===a.name);return l?{...l,displayName:a.displayName,description:a.description,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,isConfigured:a.isConfigured,toolApprovalConfig:a.toolApprovalConfig}:a})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(s=>{const a=this.transformToolDisplay(s),l=t.find(o=>o.name===s.definition.name),i=(l==null?void 0:l.isConfigured)??!a.requiresAuthentication;return{config:(l==null?void 0:l.config)??{},configString:JSON.stringify((l==null?void 0:l.config)??{},null,2),isConfigured:i,name:s.definition.name.toString(),displayName:a.displayName,description:a.description,identifier:s.identifier,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:s.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return Rr(this.configs,e=>{const t=e.filter(a=>this.isDisplayableTool(a)),s=new Map;for(const a of t)s.set(a.displayName,a);return Array.from(s.values()).sort((a,l)=>{const i={GitHub:1,Linear:2,Notion:3},o=Number.MAX_SAFE_INTEGER,c=i[a.displayName]||o,u=i[l.displayName]||o;return c<o&&u<o||c===o&&u===o?c!==u?c-u:a.displayName.localeCompare(l.displayName):c-u})})}getPretendNativeToolDefs(){return Rr(this.configs,e=>this.getEnableNativeRemoteMcp()?Yr(e):[])}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:Ge.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:Ge.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"&&this._enableNativeRemoteMcp}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}updateToolApprovalConfig(e,t){this.configs.update(s=>s.map(a=>a.identifier.toolId===e.toolId&&a.identifier.hostName===e.hostName?{...a,toolApprovalConfig:t}:a))}getSettingsComponentSupported(){return this._settingsComponentSupported}}Ie(Ms,"key","toolConfigModel");(function(r){r.assertEqual=e=>e,r.assertIs=function(e){},r.assertNever=function(e){throw new Error},r.arrayToEnum=e=>{const t={};for(const s of e)t[s]=s;return t},r.getValidEnumValues=e=>{const t=r.objectKeys(e).filter(a=>typeof e[e[a]]!="number"),s={};for(const a of t)s[a]=e[a];return r.objectValues(s)},r.objectValues=e=>r.objectKeys(e).map(function(t){return e[t]}),r.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.push(s);return t},r.find=(e,t)=>{for(const s of e)if(t(s))return s},r.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,r.joinValues=function(e,t=" | "){return e.map(s=>typeof s=="string"?`'${s}'`:s).join(t)},r.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(Pe||(Pe={})),function(r){r.mergeShapes=(e,t)=>({...e,...t})}(wr||(wr={}));const Y=Pe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Pt=r=>{switch(typeof r){case"undefined":return Y.undefined;case"string":return Y.string;case"number":return isNaN(r)?Y.nan:Y.number;case"boolean":return Y.boolean;case"function":return Y.function;case"bigint":return Y.bigint;case"symbol":return Y.symbol;case"object":return Array.isArray(r)?Y.array:r===null?Y.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?Y.promise:typeof Map<"u"&&r instanceof Map?Y.map:typeof Set<"u"&&r instanceof Set?Y.set:typeof Date<"u"&&r instanceof Date?Y.date:Y.object;default:return Y.unknown}},C=Pe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class _t extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(l){return l.message},s={_errors:[]},a=l=>{for(const i of l.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let o=s,c=0;for(;c<i.path.length;){const u=i.path[c];c===i.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(t(i))):o[u]=o[u]||{_errors:[]},o=o[u],c++}}};return a(this),s}static assert(e){if(!(e instanceof _t))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,Pe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},s=[];for(const a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):s.push(e(a));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}_t.create=r=>new _t(r);const vs=(r,e)=>{let t;switch(r.code){case C.invalid_type:t=r.received===Y.undefined?"Required":`Expected ${r.expected}, received ${r.received}`;break;case C.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,Pe.jsonStringifyReplacer)}`;break;case C.unrecognized_keys:t=`Unrecognized key(s) in object: ${Pe.joinValues(r.keys,", ")}`;break;case C.invalid_union:t="Invalid input";break;case C.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${Pe.joinValues(r.options)}`;break;case C.invalid_enum_value:t=`Invalid enum value. Expected ${Pe.joinValues(r.options)}, received '${r.received}'`;break;case C.invalid_arguments:t="Invalid function arguments";break;case C.invalid_return_type:t="Invalid function return type";break;case C.invalid_date:t="Invalid date";break;case C.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:Pe.assertNever(r.validation):t=r.validation!=="regex"?`Invalid ${r.validation}`:"Invalid";break;case C.too_small:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:"Invalid input";break;case C.too_big:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:"Invalid input";break;case C.custom:t="Invalid input";break;case C.invalid_intersection_types:t="Intersection results could not be merged";break;case C.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case C.not_finite:t="Number must be finite";break;default:t=e.defaultError,Pe.assertNever(r)}return{message:t}};let oa=vs;function Ys(){return oa}const Qs=r=>{const{data:e,path:t,errorMaps:s,issueData:a}=r,l=[...t,...a.path||[]],i={...a,path:l};if(a.message!==void 0)return{...a,path:l,message:a.message};let o="";const c=s.filter(u=>!!u).slice().reverse();for(const u of c)o=u(i,{data:e,defaultError:o}).message;return{...a,path:l,message:o}};function H(r,e){const t=Ys(),s=Qs({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===vs?void 0:vs].filter(a=>!!a)});r.common.issues.push(s)}class vt{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const a of t){if(a.status==="aborted")return fe;a.status==="dirty"&&e.dirty(),s.push(a.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const a of t){const l=await a.key,i=await a.value;s.push({key:l,value:i})}return vt.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const a of t){const{key:l,value:i}=a;if(l.status==="aborted"||i.status==="aborted")return fe;l.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),l.value==="__proto__"||i.value===void 0&&!a.alwaysSet||(s[l.value]=i.value)}return{status:e.value,value:s}}}const fe=Object.freeze({status:"aborted"}),Xs=r=>({status:"dirty",value:r}),gt=r=>({status:"valid",value:r}),Cr=r=>r.status==="aborted",br=r=>r.status==="dirty",ss=r=>r.status==="valid",As=r=>typeof Promise<"u"&&r instanceof Promise;function er(r,e,t,s){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(r)}function la(r,e,t,s,a){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(r,t),t}var ae,bs,$s;typeof SuppressedError=="function"&&SuppressedError,function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(ae||(ae={}));class It{constructor(e,t,s,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=a}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const jr=(r,e)=>{if(ss(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new _t(r.common.issues);return this._error=t,this._error}}};function ke(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:s,description:a}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(l,i)=>{var o,c;const{message:u}=r;return l.code==="invalid_enum_value"?{message:u??i.defaultError}:i.data===void 0?{message:(o=u??s)!==null&&o!==void 0?o:i.defaultError}:l.code!=="invalid_type"?{message:i.defaultError}:{message:(c=u??t)!==null&&c!==void 0?c:i.defaultError}},description:a}}class xe{get description(){return this._def.description}_getType(e){return Pt(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:Pt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new vt,ctx:{common:e.parent.common,data:e.data,parsedType:Pt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(As(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){var s;const a={common:{issues:[],async:(s=t==null?void 0:t.async)!==null&&s!==void 0&&s,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)},l=this._parseSync({data:e,path:a.path,parent:a});return jr(a,l)}"~validate"(e){var t,s;const a={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)};if(!this["~standard"].async)try{const l=this._parseSync({data:e,path:[],parent:a});return ss(l)?{value:l.value}:{issues:a.common.issues}}catch(l){!((s=(t=l==null?void 0:l.message)===null||t===void 0?void 0:t.toLowerCase())===null||s===void 0)&&s.includes("encountered")&&(this["~standard"].async=!0),a.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:a}).then(l=>ss(l)?{value:l.value}:{issues:a.common.issues})}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Pt(e)},a=this._parse({data:e,path:s.path,parent:s}),l=await(As(a)?a:Promise.resolve(a));return jr(s,l)}refine(e,t){const s=a=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(a):t;return this._refinement((a,l)=>{const i=e(a),o=()=>l.addIssue({code:C.custom,...s(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(o(),!1)):!!i||(o(),!1)})}refinement(e,t){return this._refinement((s,a)=>!!e(s)||(a.addIssue(typeof t=="function"?t(s,a):t),!1))}_refinement(e){return new Mt({schema:this,typeName:pe.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return Rt.create(this,this._def)}nullable(){return Wt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return At.create(this)}promise(){return gs.create(this,this._def)}or(e){return Es.create([this,e],this._def)}and(e){return Is.create(this,e,this._def)}transform(e){return new Mt({...ke(this._def),schema:this,typeName:pe.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new Zs({...ke(this._def),innerType:this,defaultValue:t,typeName:pe.ZodDefault})}brand(){return new Tr({typeName:pe.ZodBranded,type:this,...ke(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new Ps({...ke(this._def),innerType:this,catchValue:t,typeName:pe.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Vs.create(this,e)}readonly(){return Fs.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const bn=/^c[^\s-]{8,}$/i,$n=/^[0-9a-z]+$/,Sn=/^[0-9A-HJKMNP-TV-Z]{26}$/i,kn=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,xn=/^[a-z0-9_-]{21}$/i,Mn=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,An=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Tn=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let vr;const Nn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Rn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,En=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,In=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,zn=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Ln=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,da="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",On=new RegExp(`^${da}$`);function ca(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function ua(r){let e=`${da}T${ca(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Zn(r,e){if(!Mn.test(r))return!1;try{const[t]=r.split("."),s=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),a=JSON.parse(atob(s));return typeof a=="object"&&a!==null&&!(!a.typ||!a.alg)&&(!e||a.alg===e)}catch{return!1}}function Pn(r,e){return!(e!=="v4"&&e||!Rn.test(r))||!(e!=="v6"&&e||!In.test(r))}class xt extends xe{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==Y.string){const i=this._getOrReturnCtx(e);return H(i,{code:C.invalid_type,expected:Y.string,received:i.parsedType}),fe}const t=new vt;let s;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(s=this._getOrReturnCtx(e,s),H(s,{code:C.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(s=this._getOrReturnCtx(e,s),H(s,{code:C.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(s=this._getOrReturnCtx(e,s),o?H(s,{code:C.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&H(s,{code:C.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")Tn.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"email",code:C.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")vr||(vr=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),vr.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"emoji",code:C.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")kn.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"uuid",code:C.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")xn.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"nanoid",code:C.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")bn.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"cuid",code:C.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")$n.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"cuid2",code:C.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Sn.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"ulid",code:C.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),H(s,{validation:"url",code:C.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"regex",code:C.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(e,s),H(s,{code:C.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(s=this._getOrReturnCtx(e,s),H(s,{code:C.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(s=this._getOrReturnCtx(e,s),H(s,{code:C.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?ua(i).test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{code:C.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?On.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{code:C.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${ca(i)}$`).test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{code:C.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?An.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"duration",code:C.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(a=e.data,((l=i.version)!=="v4"&&l||!Nn.test(a))&&(l!=="v6"&&l||!En.test(a))&&(s=this._getOrReturnCtx(e,s),H(s,{validation:"ip",code:C.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Zn(e.data,i.alg)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"jwt",code:C.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Pn(e.data,i.version)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"cidr",code:C.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?zn.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"base64",code:C.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Ln.test(e.data)||(s=this._getOrReturnCtx(e,s),H(s,{validation:"base64url",code:C.invalid_string,message:i.message}),t.dirty()):Pe.assertNever(i);var a,l;return{status:t.value,value:e.data}}_regex(e,t,s){return this.refinement(a=>e.test(a),{validation:t,code:C.invalid_string,...ae.errToObj(s)})}_addCheck(e){return new xt({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...ae.errToObj(e)})}url(e){return this._addCheck({kind:"url",...ae.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...ae.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...ae.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...ae.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...ae.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...ae.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...ae.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...ae.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...ae.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...ae.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...ae.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...ae.errToObj(e)})}datetime(e){var t,s;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(s=e==null?void 0:e.local)!==null&&s!==void 0&&s,...ae.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...ae.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...ae.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...ae.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...ae.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...ae.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...ae.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...ae.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...ae.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...ae.errToObj(t)})}nonempty(e){return this.min(1,ae.errToObj(e))}trim(){return new xt({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new xt({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new xt({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function Fn(r,e){const t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,a=t>s?t:s;return parseInt(r.toFixed(a).replace(".",""))%parseInt(e.toFixed(a).replace(".",""))/Math.pow(10,a)}xt.create=r=>{var e;return new xt({checks:[],typeName:pe.ZodString,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...ke(r)})};class Bt extends xe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==Y.number){const a=this._getOrReturnCtx(e);return H(a,{code:C.invalid_type,expected:Y.number,received:a.parsedType}),fe}let t;const s=new vt;for(const a of this._def.checks)a.kind==="int"?Pe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),H(t,{code:C.invalid_type,expected:"integer",received:"float",message:a.message}),s.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),H(t,{code:C.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),H(t,{code:C.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="multipleOf"?Fn(e.data,a.value)!==0&&(t=this._getOrReturnCtx(e,t),H(t,{code:C.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),H(t,{code:C.not_finite,message:a.message}),s.dirty()):Pe.assertNever(a);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,ae.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ae.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ae.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ae.toString(t))}setLimit(e,t,s,a){return new Bt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:ae.toString(a)}]})}_addCheck(e){return new Bt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:ae.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:ae.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:ae.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:ae.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:ae.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ae.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:ae.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:ae.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:ae.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&Pe.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Bt.create=r=>new Bt({checks:[],typeName:pe.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...ke(r)});class Gt extends xe{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==Y.bigint)return this._getInvalidInput(e);let t;const s=new vt;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),H(t,{code:C.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),H(t,{code:C.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),H(t,{code:C.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):Pe.assertNever(a);return{status:s.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return H(t,{code:C.invalid_type,expected:Y.bigint,received:t.parsedType}),fe}gte(e,t){return this.setLimit("min",e,!0,ae.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ae.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ae.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ae.toString(t))}setLimit(e,t,s,a){return new Gt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:ae.toString(a)}]})}_addCheck(e){return new Gt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:ae.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:ae.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:ae.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:ae.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ae.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Gt.create=r=>{var e;return new Gt({checks:[],typeName:pe.ZodBigInt,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...ke(r)})};class Ts extends xe{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==Y.boolean){const t=this._getOrReturnCtx(e);return H(t,{code:C.invalid_type,expected:Y.boolean,received:t.parsedType}),fe}return gt(e.data)}}Ts.create=r=>new Ts({typeName:pe.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...ke(r)});class rs extends xe{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==Y.date){const a=this._getOrReturnCtx(e);return H(a,{code:C.invalid_type,expected:Y.date,received:a.parsedType}),fe}if(isNaN(e.data.getTime()))return H(this._getOrReturnCtx(e),{code:C.invalid_date}),fe;const t=new vt;let s;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(s=this._getOrReturnCtx(e,s),H(s,{code:C.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(s=this._getOrReturnCtx(e,s),H(s,{code:C.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):Pe.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new rs({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:ae.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:ae.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}rs.create=r=>new rs({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:pe.ZodDate,...ke(r)});class tr extends xe{_parse(e){if(this._getType(e)!==Y.symbol){const t=this._getOrReturnCtx(e);return H(t,{code:C.invalid_type,expected:Y.symbol,received:t.parsedType}),fe}return gt(e.data)}}tr.create=r=>new tr({typeName:pe.ZodSymbol,...ke(r)});class Ns extends xe{_parse(e){if(this._getType(e)!==Y.undefined){const t=this._getOrReturnCtx(e);return H(t,{code:C.invalid_type,expected:Y.undefined,received:t.parsedType}),fe}return gt(e.data)}}Ns.create=r=>new Ns({typeName:pe.ZodUndefined,...ke(r)});class Rs extends xe{_parse(e){if(this._getType(e)!==Y.null){const t=this._getOrReturnCtx(e);return H(t,{code:C.invalid_type,expected:Y.null,received:t.parsedType}),fe}return gt(e.data)}}Rs.create=r=>new Rs({typeName:pe.ZodNull,...ke(r)});class ms extends xe{constructor(){super(...arguments),this._any=!0}_parse(e){return gt(e.data)}}ms.create=r=>new ms({typeName:pe.ZodAny,...ke(r)});class es extends xe{constructor(){super(...arguments),this._unknown=!0}_parse(e){return gt(e.data)}}es.create=r=>new es({typeName:pe.ZodUnknown,...ke(r)});class Dt extends xe{_parse(e){const t=this._getOrReturnCtx(e);return H(t,{code:C.invalid_type,expected:Y.never,received:t.parsedType}),fe}}Dt.create=r=>new Dt({typeName:pe.ZodNever,...ke(r)});class sr extends xe{_parse(e){if(this._getType(e)!==Y.undefined){const t=this._getOrReturnCtx(e);return H(t,{code:C.invalid_type,expected:Y.void,received:t.parsedType}),fe}return gt(e.data)}}sr.create=r=>new sr({typeName:pe.ZodVoid,...ke(r)});class At extends xe{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),a=this._def;if(t.parsedType!==Y.array)return H(t,{code:C.invalid_type,expected:Y.array,received:t.parsedType}),fe;if(a.exactLength!==null){const i=t.data.length>a.exactLength.value,o=t.data.length<a.exactLength.value;(i||o)&&(H(t,{code:i?C.too_big:C.too_small,minimum:o?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),s.dirty())}if(a.minLength!==null&&t.data.length<a.minLength.value&&(H(t,{code:C.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),s.dirty()),a.maxLength!==null&&t.data.length>a.maxLength.value&&(H(t,{code:C.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>a.type._parseAsync(new It(t,i,t.path,o)))).then(i=>vt.mergeArray(s,i));const l=[...t.data].map((i,o)=>a.type._parseSync(new It(t,i,t.path,o)));return vt.mergeArray(s,l)}get element(){return this._def.type}min(e,t){return new At({...this._def,minLength:{value:e,message:ae.toString(t)}})}max(e,t){return new At({...this._def,maxLength:{value:e,message:ae.toString(t)}})}length(e,t){return new At({...this._def,exactLength:{value:e,message:ae.toString(t)}})}nonempty(e){return this.min(1,e)}}function is(r){if(r instanceof Ke){const e={};for(const t in r.shape){const s=r.shape[t];e[t]=Rt.create(is(s))}return new Ke({...r._def,shape:()=>e})}return r instanceof At?new At({...r._def,type:is(r.element)}):r instanceof Rt?Rt.create(is(r.unwrap())):r instanceof Wt?Wt.create(is(r.unwrap())):r instanceof zt?zt.create(r.items.map(e=>is(e))):r}At.create=(r,e)=>new At({type:r,minLength:null,maxLength:null,exactLength:null,typeName:pe.ZodArray,...ke(e)});class Ke extends xe{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=Pe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==Y.object){const c=this._getOrReturnCtx(e);return H(c,{code:C.invalid_type,expected:Y.object,received:c.parsedType}),fe}const{status:t,ctx:s}=this._processInputParams(e),{shape:a,keys:l}=this._getCached(),i=[];if(!(this._def.catchall instanceof Dt&&this._def.unknownKeys==="strip"))for(const c in s.data)l.includes(c)||i.push(c);const o=[];for(const c of l){const u=a[c],M=s.data[c];o.push({key:{status:"valid",value:c},value:u._parse(new It(s,M,s.path,c)),alwaysSet:c in s.data})}if(this._def.catchall instanceof Dt){const c=this._def.unknownKeys;if(c==="passthrough")for(const u of i)o.push({key:{status:"valid",value:u},value:{status:"valid",value:s.data[u]}});else if(c==="strict")i.length>0&&(H(s,{code:C.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const u of i){const M=s.data[u];o.push({key:{status:"valid",value:u},value:c._parse(new It(s,M,s.path,u)),alwaysSet:u in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const c=[];for(const u of o){const M=await u.key,oe=await u.value;c.push({key:M,value:oe,alwaysSet:u.alwaysSet})}return c}).then(c=>vt.mergeObjectSync(t,c)):vt.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return ae.errToObj,new Ke({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{var a,l,i,o;const c=(i=(l=(a=this._def).errorMap)===null||l===void 0?void 0:l.call(a,t,s).message)!==null&&i!==void 0?i:s.defaultError;return t.code==="unrecognized_keys"?{message:(o=ae.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new Ke({...this._def,unknownKeys:"strip"})}passthrough(){return new Ke({...this._def,unknownKeys:"passthrough"})}extend(e){return new Ke({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Ke({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:pe.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new Ke({...this._def,catchall:e})}pick(e){const t={};return Pe.objectKeys(e).forEach(s=>{e[s]&&this.shape[s]&&(t[s]=this.shape[s])}),new Ke({...this._def,shape:()=>t})}omit(e){const t={};return Pe.objectKeys(this.shape).forEach(s=>{e[s]||(t[s]=this.shape[s])}),new Ke({...this._def,shape:()=>t})}deepPartial(){return is(this)}partial(e){const t={};return Pe.objectKeys(this.shape).forEach(s=>{const a=this.shape[s];e&&!e[s]?t[s]=a:t[s]=a.optional()}),new Ke({...this._def,shape:()=>t})}required(e){const t={};return Pe.objectKeys(this.shape).forEach(s=>{if(e&&!e[s])t[s]=this.shape[s];else{let a=this.shape[s];for(;a instanceof Rt;)a=a._def.innerType;t[s]=a}}),new Ke({...this._def,shape:()=>t})}keyof(){return ha(Pe.objectKeys(this.shape))}}Ke.create=(r,e)=>new Ke({shape:()=>r,unknownKeys:"strip",catchall:Dt.create(),typeName:pe.ZodObject,...ke(e)}),Ke.strictCreate=(r,e)=>new Ke({shape:()=>r,unknownKeys:"strict",catchall:Dt.create(),typeName:pe.ZodObject,...ke(e)}),Ke.lazycreate=(r,e)=>new Ke({shape:r,unknownKeys:"strip",catchall:Dt.create(),typeName:pe.ZodObject,...ke(e)});class Es extends xe{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;if(t.common.async)return Promise.all(s.map(async a=>{const l={...t,common:{...t.common,issues:[]},parent:null};return{result:await a._parseAsync({data:t.data,path:t.path,parent:l}),ctx:l}})).then(function(a){for(const i of a)if(i.result.status==="valid")return i.result;for(const i of a)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const l=a.map(i=>new _t(i.ctx.common.issues));return H(t,{code:C.invalid_union,unionErrors:l}),fe});{let a;const l=[];for(const o of s){const c={...t,common:{...t.common,issues:[]},parent:null},u=o._parseSync({data:t.data,path:t.path,parent:c});if(u.status==="valid")return u;u.status!=="dirty"||a||(a={result:u,ctx:c}),c.common.issues.length&&l.push(c.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const i=l.map(o=>new _t(o));return H(t,{code:C.invalid_union,unionErrors:i}),fe}}get options(){return this._def.options}}Es.create=(r,e)=>new Es({options:r,typeName:pe.ZodUnion,...ke(e)});const Ut=r=>r instanceof zs?Ut(r.schema):r instanceof Mt?Ut(r.innerType()):r instanceof Ls?[r.value]:r instanceof Jt?r.options:r instanceof Os?Pe.objectValues(r.enum):r instanceof Zs?Ut(r._def.innerType):r instanceof Ns?[void 0]:r instanceof Rs?[null]:r instanceof Rt?[void 0,...Ut(r.unwrap())]:r instanceof Wt?[null,...Ut(r.unwrap())]:r instanceof Tr||r instanceof Fs?Ut(r.unwrap()):r instanceof Ps?Ut(r._def.innerType):[];class ir extends xe{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Y.object)return H(t,{code:C.invalid_type,expected:Y.object,received:t.parsedType}),fe;const s=this.discriminator,a=t.data[s],l=this.optionsMap.get(a);return l?t.common.async?l._parseAsync({data:t.data,path:t.path,parent:t}):l._parseSync({data:t.data,path:t.path,parent:t}):(H(t,{code:C.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),fe)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,s){const a=new Map;for(const l of t){const i=Ut(l.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(a.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);a.set(o,l)}}return new ir({typeName:pe.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...ke(s)})}}function $r(r,e){const t=Pt(r),s=Pt(e);if(r===e)return{valid:!0,data:r};if(t===Y.object&&s===Y.object){const a=Pe.objectKeys(e),l=Pe.objectKeys(r).filter(o=>a.indexOf(o)!==-1),i={...r,...e};for(const o of l){const c=$r(r[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===Y.array&&s===Y.array){if(r.length!==e.length)return{valid:!1};const a=[];for(let l=0;l<r.length;l++){const i=$r(r[l],e[l]);if(!i.valid)return{valid:!1};a.push(i.data)}return{valid:!0,data:a}}return t===Y.date&&s===Y.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class Is extends xe{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=(l,i)=>{if(Cr(l)||Cr(i))return fe;const o=$r(l.value,i.value);return o.valid?((br(l)||br(i))&&t.dirty(),{status:t.value,value:o.data}):(H(s,{code:C.invalid_intersection_types}),fe)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([l,i])=>a(l,i)):a(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}Is.create=(r,e,t)=>new Is({left:r,right:e,typeName:pe.ZodIntersection,...ke(t)});class zt extends xe{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==Y.array)return H(s,{code:C.invalid_type,expected:Y.array,received:s.parsedType}),fe;if(s.data.length<this._def.items.length)return H(s,{code:C.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),fe;!this._def.rest&&s.data.length>this._def.items.length&&(H(s,{code:C.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...s.data].map((l,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new It(s,l,s.path,i)):null}).filter(l=>!!l);return s.common.async?Promise.all(a).then(l=>vt.mergeArray(t,l)):vt.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new zt({...this._def,rest:e})}}zt.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new zt({items:r,typeName:pe.ZodTuple,rest:null,...ke(e)})};class or extends xe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==Y.object)return H(s,{code:C.invalid_type,expected:Y.object,received:s.parsedType}),fe;const a=[],l=this._def.keyType,i=this._def.valueType;for(const o in s.data)a.push({key:l._parse(new It(s,o,s.path,o)),value:i._parse(new It(s,s.data[o],s.path,o)),alwaysSet:o in s.data});return s.common.async?vt.mergeObjectAsync(t,a):vt.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,s){return new or(t instanceof xe?{keyType:e,valueType:t,typeName:pe.ZodRecord,...ke(s)}:{keyType:xt.create(),valueType:e,typeName:pe.ZodRecord,...ke(t)})}}class rr extends xe{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==Y.map)return H(s,{code:C.invalid_type,expected:Y.map,received:s.parsedType}),fe;const a=this._def.keyType,l=this._def.valueType,i=[...s.data.entries()].map(([o,c],u)=>({key:a._parse(new It(s,o,s.path,[u,"key"])),value:l._parse(new It(s,c,s.path,[u,"value"]))}));if(s.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of i){const u=await c.key,M=await c.value;if(u.status==="aborted"||M.status==="aborted")return fe;u.status!=="dirty"&&M.status!=="dirty"||t.dirty(),o.set(u.value,M.value)}return{status:t.value,value:o}})}{const o=new Map;for(const c of i){const u=c.key,M=c.value;if(u.status==="aborted"||M.status==="aborted")return fe;u.status!=="dirty"&&M.status!=="dirty"||t.dirty(),o.set(u.value,M.value)}return{status:t.value,value:o}}}}rr.create=(r,e,t)=>new rr({valueType:e,keyType:r,typeName:pe.ZodMap,...ke(t)});class as extends xe{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==Y.set)return H(s,{code:C.invalid_type,expected:Y.set,received:s.parsedType}),fe;const a=this._def;a.minSize!==null&&s.data.size<a.minSize.value&&(H(s,{code:C.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),a.maxSize!==null&&s.data.size>a.maxSize.value&&(H(s,{code:C.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const l=this._def.valueType;function i(c){const u=new Set;for(const M of c){if(M.status==="aborted")return fe;M.status==="dirty"&&t.dirty(),u.add(M.value)}return{status:t.value,value:u}}const o=[...s.data.values()].map((c,u)=>l._parse(new It(s,c,s.path,u)));return s.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new as({...this._def,minSize:{value:e,message:ae.toString(t)}})}max(e,t){return new as({...this._def,maxSize:{value:e,message:ae.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}as.create=(r,e)=>new as({valueType:r,minSize:null,maxSize:null,typeName:pe.ZodSet,...ke(e)});class cs extends xe{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Y.function)return H(t,{code:C.invalid_type,expected:Y.function,received:t.parsedType}),fe;function s(o,c){return Qs({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Ys(),vs].filter(u=>!!u),issueData:{code:C.invalid_arguments,argumentsError:c}})}function a(o,c){return Qs({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Ys(),vs].filter(u=>!!u),issueData:{code:C.invalid_return_type,returnTypeError:c}})}const l={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof gs){const o=this;return gt(async function(...c){const u=new _t([]),M=await o._def.args.parseAsync(c,l).catch(E=>{throw u.addIssue(s(c,E)),u}),oe=await Reflect.apply(i,this,M);return await o._def.returns._def.type.parseAsync(oe,l).catch(E=>{throw u.addIssue(a(oe,E)),u})})}{const o=this;return gt(function(...c){const u=o._def.args.safeParse(c,l);if(!u.success)throw new _t([s(c,u.error)]);const M=Reflect.apply(i,this,u.data),oe=o._def.returns.safeParse(M,l);if(!oe.success)throw new _t([a(M,oe.error)]);return oe.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new cs({...this._def,args:zt.create(e).rest(es.create())})}returns(e){return new cs({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,s){return new cs({args:e||zt.create([]).rest(es.create()),returns:t||es.create(),typeName:pe.ZodFunction,...ke(s)})}}class zs extends xe{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}zs.create=(r,e)=>new zs({getter:r,typeName:pe.ZodLazy,...ke(e)});class Ls extends xe{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return H(t,{received:t.data,code:C.invalid_literal,expected:this._def.value}),fe}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ha(r,e){return new Jt({values:r,typeName:pe.ZodEnum,...ke(e)})}Ls.create=(r,e)=>new Ls({value:r,typeName:pe.ZodLiteral,...ke(e)});class Jt extends xe{constructor(){super(...arguments),bs.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),s=this._def.values;return H(t,{expected:Pe.joinValues(s),received:t.parsedType,code:C.invalid_type}),fe}if(er(this,bs)||la(this,bs,new Set(this._def.values)),!er(this,bs).has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return H(t,{received:t.data,code:C.invalid_enum_value,options:s}),fe}return gt(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Jt.create(e,{...this._def,...t})}exclude(e,t=this._def){return Jt.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}}bs=new WeakMap,Jt.create=ha;class Os extends xe{constructor(){super(...arguments),$s.set(this,void 0)}_parse(e){const t=Pe.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==Y.string&&s.parsedType!==Y.number){const a=Pe.objectValues(t);return H(s,{expected:Pe.joinValues(a),received:s.parsedType,code:C.invalid_type}),fe}if(er(this,$s)||la(this,$s,new Set(Pe.getValidEnumValues(this._def.values))),!er(this,$s).has(e.data)){const a=Pe.objectValues(t);return H(s,{received:s.data,code:C.invalid_enum_value,options:a}),fe}return gt(e.data)}get enum(){return this._def.values}}$s=new WeakMap,Os.create=(r,e)=>new Os({values:r,typeName:pe.ZodNativeEnum,...ke(e)});class gs extends xe{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==Y.promise&&t.common.async===!1)return H(t,{code:C.invalid_type,expected:Y.promise,received:t.parsedType}),fe;const s=t.parsedType===Y.promise?t.data:Promise.resolve(t.data);return gt(s.then(a=>this._def.type.parseAsync(a,{path:t.path,errorMap:t.common.contextualErrorMap})))}}gs.create=(r,e)=>new gs({type:r,typeName:pe.ZodPromise,...ke(e)});class Mt extends xe{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===pe.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=this._def.effect||null,l={addIssue:i=>{H(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(l.addIssue=l.addIssue.bind(l),a.type==="preprocess"){const i=a.transform(s.data,l);if(s.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return fe;const c=await this._def.schema._parseAsync({data:o,path:s.path,parent:s});return c.status==="aborted"?fe:c.status==="dirty"||t.value==="dirty"?Xs(c.value):c});{if(t.value==="aborted")return fe;const o=this._def.schema._parseSync({data:i,path:s.path,parent:s});return o.status==="aborted"?fe:o.status==="dirty"||t.value==="dirty"?Xs(o.value):o}}if(a.type==="refinement"){const i=o=>{const c=a.refinement(o,l);if(s.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(s.common.async===!1){const o=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return o.status==="aborted"?fe:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(o=>o.status==="aborted"?fe:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(a.type==="transform"){if(s.common.async===!1){const i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!ss(i))return i;const o=a.transform(i.value,l);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>ss(i)?Promise.resolve(a.transform(i.value,l)).then(o=>({status:t.value,value:o})):i)}Pe.assertNever(a)}}Mt.create=(r,e,t)=>new Mt({schema:r,typeName:pe.ZodEffects,effect:e,...ke(t)}),Mt.createWithPreprocess=(r,e,t)=>new Mt({schema:e,effect:{type:"preprocess",transform:r},typeName:pe.ZodEffects,...ke(t)});class Rt extends xe{_parse(e){return this._getType(e)===Y.undefined?gt(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Rt.create=(r,e)=>new Rt({innerType:r,typeName:pe.ZodOptional,...ke(e)});class Wt extends xe{_parse(e){return this._getType(e)===Y.null?gt(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Wt.create=(r,e)=>new Wt({innerType:r,typeName:pe.ZodNullable,...ke(e)});class Zs extends xe{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===Y.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Zs.create=(r,e)=>new Zs({innerType:r,typeName:pe.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ke(e)});class Ps extends xe{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return As(a)?a.then(l=>({status:"valid",value:l.status==="valid"?l.value:this._def.catchValue({get error(){return new _t(s.common.issues)},input:s.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new _t(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}Ps.create=(r,e)=>new Ps({innerType:r,typeName:pe.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ke(e)});class ar extends xe{_parse(e){if(this._getType(e)!==Y.nan){const t=this._getOrReturnCtx(e);return H(t,{code:C.invalid_type,expected:Y.nan,received:t.parsedType}),fe}return{status:"valid",value:e.data}}}ar.create=r=>new ar({typeName:pe.ZodNaN,...ke(r)});const jn=Symbol("zod_brand");class Tr extends xe{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class Vs extends xe{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{const a=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?fe:a.status==="dirty"?(t.dirty(),Xs(a.value)):this._def.out._parseAsync({data:a.value,path:s.path,parent:s})})();{const a=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?fe:a.status==="dirty"?(t.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:s.path,parent:s})}}static create(e,t){return new Vs({in:e,out:t,typeName:pe.ZodPipeline})}}class Fs extends xe{_parse(e){const t=this._def.innerType._parse(e),s=a=>(ss(a)&&(a.value=Object.freeze(a.value)),a);return As(t)?t.then(a=>s(a)):s(t)}unwrap(){return this._def.innerType}}function Dr(r,e={},t){return r?ms.create().superRefine((s,a)=>{var l,i;if(!r(s)){const o=typeof e=="function"?e(s):typeof e=="string"?{message:e}:e,c=(i=(l=o.fatal)!==null&&l!==void 0?l:t)===null||i===void 0||i,u=typeof o=="string"?{message:o}:o;a.addIssue({code:"custom",...u,fatal:c})}}):ms.create()}Fs.create=(r,e)=>new Fs({innerType:r,typeName:pe.ZodReadonly,...ke(e)});const Dn={object:Ke.lazycreate};var pe;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(pe||(pe={}));const Vr=xt.create,Ur=Bt.create,Vn=ar.create,Un=Gt.create,qr=Ts.create,qn=rs.create,Hn=tr.create,Bn=Ns.create,Gn=Rs.create,Jn=ms.create,Wn=es.create,Kn=Dt.create,Yn=sr.create,Qn=At.create,Xn=Ke.create,ei=Ke.strictCreate,ti=Es.create,si=ir.create,ri=Is.create,ai=zt.create,ni=or.create,ii=rr.create,oi=as.create,li=cs.create,di=zs.create,ci=Ls.create,ui=Jt.create,hi=Os.create,pi=gs.create,Hr=Mt.create,vi=Rt.create,mi=Wt.create,gi=Mt.createWithPreprocess,fi=Vs.create,yi={string:r=>xt.create({...r,coerce:!0}),number:r=>Bt.create({...r,coerce:!0}),boolean:r=>Ts.create({...r,coerce:!0}),bigint:r=>Gt.create({...r,coerce:!0}),date:r=>rs.create({...r,coerce:!0})},_i=fe;var je=Object.freeze({__proto__:null,defaultErrorMap:vs,setErrorMap:function(r){oa=r},getErrorMap:Ys,makeIssue:Qs,EMPTY_PATH:[],addIssueToContext:H,ParseStatus:vt,INVALID:fe,DIRTY:Xs,OK:gt,isAborted:Cr,isDirty:br,isValid:ss,isAsync:As,get util(){return Pe},get objectUtil(){return wr},ZodParsedType:Y,getParsedType:Pt,ZodType:xe,datetimeRegex:ua,ZodString:xt,ZodNumber:Bt,ZodBigInt:Gt,ZodBoolean:Ts,ZodDate:rs,ZodSymbol:tr,ZodUndefined:Ns,ZodNull:Rs,ZodAny:ms,ZodUnknown:es,ZodNever:Dt,ZodVoid:sr,ZodArray:At,ZodObject:Ke,ZodUnion:Es,ZodDiscriminatedUnion:ir,ZodIntersection:Is,ZodTuple:zt,ZodRecord:or,ZodMap:rr,ZodSet:as,ZodFunction:cs,ZodLazy:zs,ZodLiteral:Ls,ZodEnum:Jt,ZodNativeEnum:Os,ZodPromise:gs,ZodEffects:Mt,ZodTransformer:Mt,ZodOptional:Rt,ZodNullable:Wt,ZodDefault:Zs,ZodCatch:Ps,ZodNaN:ar,BRAND:jn,ZodBranded:Tr,ZodPipeline:Vs,ZodReadonly:Fs,custom:Dr,Schema:xe,ZodSchema:xe,late:Dn,get ZodFirstPartyTypeKind(){return pe},coerce:yi,any:Jn,array:Qn,bigint:Un,boolean:qr,date:qn,discriminatedUnion:si,effect:Hr,enum:ui,function:li,instanceof:(r,e={message:`Input not instance of ${r.name}`})=>Dr(t=>t instanceof r,e),intersection:ri,lazy:di,literal:ci,map:ii,nan:Vn,nativeEnum:hi,never:Kn,null:Gn,nullable:mi,number:Ur,object:Xn,oboolean:()=>qr().optional(),onumber:()=>Ur().optional(),optional:vi,ostring:()=>Vr().optional(),pipeline:fi,preprocess:gi,promise:pi,record:ni,set:oi,strictObject:ei,string:Vr,symbol:Hn,transformer:Hr,tuple:ai,undefined:Bn,union:ti,unknown:Wn,void:Yn,NEVER:_i,ZodIssueCode:C,quotelessJson:r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:_t});class pt extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,pt.prototype)}}const jt=je.object({name:je.string().optional(),title:je.string().optional(),type:je.enum(["stdio","http","sse"]).optional(),command:je.string().optional(),args:je.array(je.union([je.string(),je.number(),je.boolean()])).optional(),env:je.record(je.union([je.string(),je.number(),je.boolean(),je.null(),je.undefined()])).optional(),url:je.string().optional()}).passthrough();function kt(r){return(r==null?void 0:r.type)==="http"||(r==null?void 0:r.type)==="sse"}function os(r){return(r==null?void 0:r.type)==="stdio"}function Gs(r){return kt(r)?r.url:os(r)?r.command:""}const wi=je.array(jt),Ci=je.object({servers:je.array(jt)}),bi=je.object({mcpServers:je.array(jt)}),$i=je.object({servers:je.record(je.unknown())}),Si=je.object({mcpServers:je.record(je.unknown())}),ki=je.record(je.unknown()),xi=jt.refine(r=>{const e=r.command!==void 0,t=r.url!==void 0;if(!e&&!t)return!1;const s=new Set(["name","title","type","command","args","env","url"]);return Object.keys(r).every(a=>s.has(a))},{message:"Single server object must have valid server properties"});function Yt(r){try{const e=jt.transform(t=>{let s;if(t.type)s=t.type;else if(t.url)s="http";else{if(!t.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");s="stdio"}if(s==="http"||s==="sse"){if(!t.url)throw new Error(`${s.toUpperCase()} server must have a 'url' property`);return{type:s,name:t.name||t.title||t.url,url:t.url}}{const a=t.command||"",l=t.args?t.args.map(u=>String(u)):[];if(!a)throw new Error("Stdio server must have a 'command' property");const i=l.length>0?`${a} ${l.join(" ")}`:a,o=t.name||t.title||(a?a.split(" ")[0]:""),c=t.env?Object.fromEntries(Object.entries(t.env).filter(([u,M])=>M!=null).map(([u,M])=>[u,String(M)])):void 0;return{type:"stdio",name:o,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>t.type==="http"||t.type==="sse"?!!t.url:!!t.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(r);if(!e.success)throw new pt(e.error.message);return e.data}catch(e){throw e instanceof Error?new pt(`Invalid server configuration: ${e.message}`):new pt("Invalid server configuration")}}class Us{constructor(e){Ie(this,"servers",ut([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===Ge.getStoredMCPServersResponse){const s=t.data;return Array.isArray(s)&&this.servers.set(s),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:Ge.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:Ge.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new pt("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const s=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(s),s})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const s=[...t,...e.map(a=>({...a,id:crypto.randomUUID()}))];return this.saveServers(s),s})}checkExistingServerName(e,t){const s=mr(this.servers).find(a=>a.name===e);if(s&&(s==null?void 0:s.id)!==t)throw new pt(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const s=t.map(a=>a.id===e.id?e:a);return this.saveServers(s),s})}deleteServer(e){this.servers.update(t=>{const s=t.filter(a=>a.id!==e);return this.saveServers(s),s})}toggleDisabledServer(e){this.servers.update(t=>{const s=t.map(a=>a.id===e?{...a,disabled:!a.disabled}:a);return this.saveServers(s),s})}static convertServerToJSON(e){if(kt(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const t=e;return JSON.stringify({mcpServers:{[t.name]:{command:t.command.split(" ")[0],args:t.command.split(" ").slice(1),env:t.env}}},null,2)}}static parseServerValidationMessages(e){const t=new Map,s=new Map;e.forEach(l=>{var o,c;const i=(o=l.tools)==null?void 0:o.filter(u=>!u.enabled).map(u=>u.definition.mcp_tool_name);l.disabled?t.set(l.id,"MCP server has been manually disabled"):l.tools&&l.tools.length===0?t.set(l.id,"No tools are available for this MCP server"):i&&i.length===((c=l.tools)==null?void 0:c.length)?t.set(l.id,"All tools for this MCP server have validation errors: "+i.join(", ")):i&&i.length>0&&s.set(l.id,"MCP server has validation errors in the following tools which have been disabled: "+i.join(", "))});const a=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...a]),warnings:s}}static parseDuplicateServerIds(e){const t=new Map;for(const a of e)t.has(a.name)||t.set(a.name,[]),t.get(a.name).push(a.id);const s=new Map;for(const[,a]of t)if(a.length>1)for(let l=1;l<a.length;l++)s.set(a[l],"MCP server is disabled due to duplicate server names");return s}static convertParsedServerToWebview(e){const{tools:t,...s}=e;return{...s,tools:void 0}}static parseServerConfigFromJSON(e){return function(s){try{const a=JSON.parse(s),l=je.union([wi.transform(i=>i.map(o=>Yt(o))),Ci.transform(i=>i.servers.map(o=>Yt(o))),bi.transform(i=>i.mcpServers.map(o=>Yt(o))),$i.transform(i=>Object.entries(i.servers).map(([o,c])=>{const u=jt.parse(c);return Yt({...u,name:u.name||o})})),Si.transform(i=>Object.entries(i.mcpServers).map(([o,c])=>{const u=jt.parse(c);return Yt({...u,name:u.name||o})})),xi.transform(i=>[Yt(i)]),ki.transform(i=>{if(!Object.values(i).some(o=>{const c=jt.safeParse(o);return c.success&&(c.data.command!==void 0||c.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(i).map(([o,c])=>{const u=jt.parse(c);return Yt({...u,name:u.name||o})})})]).safeParse(a);if(l.success)return l.data;throw new pt("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(a){throw a instanceof pt?a:new pt("Failed to parse MCP servers from JSON. Please check the format.")}}(e).map(s=>this.convertParsedServerToWebview(s))}importFromJSON(e){try{const t=Us.parseServerConfigFromJSON(e),s=mr(this.servers),a=new Set(s.map(l=>l.name));for(const l of t){if(!l.name)throw new pt("All servers must have a name.");if(a.has(l.name))throw new pt(`A server with the name '${l.name}' already exists.`);a.add(l.name)}return this.servers.update(l=>{const i=[...l,...t.map(o=>({...o,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof pt?t:new pt("Failed to import MCP servers from JSON. Please check the format.")}}}class Mi{constructor(e){Ie(this,"_terminalSettings",ut({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===Ge.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:Ge.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:Ge.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:Ge.updateTerminalSettings,data:{startupScript:e}})}}const Ss=class Ss{constructor(e){Ie(this,"_swarmModeSettings",ut(Bs));Ie(this,"_isLoaded",!1);Ie(this,"_pollInterval",null);Ie(this,"_lastKnownSettingsHash","");Ie(this,"dispose",()=>{this.stopPolling()});this._msgBroker=e,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:hr.getSwarmModeSettings});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data)),this._isLoaded=!0}catch(e){console.warn("Failed to load swarm mode settings, using defaults:",e),this._swarmModeSettings.set(Bs),this._lastKnownSettingsHash=JSON.stringify(Bs),this._isLoaded=!0}}async updateSettings(e){try{const t=await this._msgBroker.sendToSidecar({type:hr.updateSwarmModeSettings,data:e});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data))}catch(t){throw console.error("Failed to update swarm mode settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async resetToDefaults(){await this.updateSettings(Bs)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},Ss.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const e=await this._msgBroker.sendToSidecar({type:hr.getSwarmModeSettings}),t=JSON.stringify(e.data);this._lastKnownSettingsHash&&t!==this._lastKnownSettingsHash&&e.data&&this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=t}catch(e){console.warn("Failed to check for swarm mode settings updates:",e)}}};Ie(Ss,"key","swarmModeModel"),Ie(Ss,"POLLING_INTERVAL_MS",5e3);let js=Ss;var Tt=(r=>(r.file="file",r.folder="folder",r))(Tt||{});class Ht{constructor(e,t){Ie(this,"subscribe");Ie(this,"set");Ie(this,"update");Ie(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case Ge.wsContextSourceFoldersChanged:case Ge.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case Ge.sourceFoldersSyncStatus:this.update(s=>({...s,syncStatus:t.data.status}))}});Ie(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:Ge.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);Ie(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:Ge.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,s)=>t.type===s.type?t.name.localeCompare(s.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:s,set:a,update:l}=ut({sourceFolders:[],sourceTree:[],syncStatus:gr.done});this.subscribe=s,this.set=a,this.update=l,this.getSourceFolders().then(i=>{this.update(o=>({...o,sourceFolders:i,sourceTree:Ht.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==wt.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:Ge.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:Ge.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:Ge.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=mr(this);const s=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(a=>({...a,sourceFolders:e,sourceTree:s}))}async getRefreshedSourceTree(e,t){const s=Ht.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,s)}async getRefreshedSourceTreeRecurse(e,t){const s=new Map(e.map(a=>[JSON.stringify([a.fileId.folderRoot,a.fileId.relPath]),a]));for(let a of t){const l=Ht.fileIdToString(a.fileId);if(a.type==="folder"){const i=s.get(l);i&&(a.expanded=i.type==="folder"&&i.expanded,a.expanded&&(a.children=await this.getChildren(a.fileId),a.children=await this.getRefreshedSourceTreeRecurse(i.children,a.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,s)=>t.name.localeCompare(s.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}var Ai=g('<div><!> <!> <span class="name svelte-1skknri"> <span class="folderRoot svelte-1skknri"> </span></span> <!></div>'),Ti=g('<div class="source-folder svelte-1skknri"><!> <div role="button" tabindex="0" class="add-more svelte-1skknri"><!> Add more...</div></div>');const Ni="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",Ri="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",Ei="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e";var Ii=g('<div class="children-container"></div>'),zi=g('<div><div role="treeitem" aria-selected="false" tabindex="0"><!> <span class="name svelte-sympus"> </span> <!> <img/></div> <!></div>');function pa(r,e){Xe(e,!1);let t=w(e,"data",8),s=w(e,"wsContextModel",8),a=w(e,"indentLevel",8);const l=()=>{s().toggleNode(t())},i={[wt.included]:Ni,[wt.excluded]:Ri,[wt.partial]:Ei},o={[wt.included]:"included",[wt.excluded]:"excluded",[wt.partial]:"partially included"};let c=G(),u=G(),M=G();_e(()=>_(t()),()=>{var N;m(u,(N=t()).type===Tt.folder&&N.inclusionState!==wt.excluded?N.expanded?"chevron-down":"chevron-right":N.type===Tt.folder?"folder":"file")}),_e(()=>(_(t()),wt),()=>{m(c,t().type===Tt.folder&&t().inclusionState!==wt.excluded)}),_e(()=>(_(t()),Tt),()=>{m(M,t().type===Tt.folder&&t().expanded&&t().children&&t().children.length>0?t():null)}),ct(),tt();var oe=zi(),E=h(oe),ce=ls(()=>Js("Enter",l));let ve;var A=h(E);sa(A,{get icon(){return n(u)}});var T=p(A,2),v=h(T),J=p(T,2),V=N=>{le(N,{size:1,class:"file-count",children:($,R)=>{var B=Z();ge(L=>Ae(B,L),[()=>(_(t()),y(()=>t().trackedFileCount.toLocaleString()))],$e),d($,B)},$$slots:{default:!0}})};W(J,N=>{_(t()),_(Tt),_(wt),y(()=>t().type===Tt.folder&&t().inclusionState!==wt.excluded&&typeof t().trackedFileCount=="number")&&N(V)});var b=p(J,2),I=p(E,2),j=N=>{var $=Ii();dt($,5,()=>(n(M),y(()=>n(M).children)),R=>Ht.fileIdToString(R.fileId),(R,B)=>{var L=it(),f=Ce(L);const k=$e(()=>a()+1);pa(f,{get data(){return n(B)},get wsContextModel(){return s()},get indentLevel(){return n(k)}}),d(R,L)}),d(N,$)};W(I,N=>{n(M)&&N(j)}),ge(N=>{ve=bt(E,1,"tree-item svelte-sympus",null,ve,N),Qt(E,"title",(_(t()),y(()=>t().reason))),Qt(E,"aria-expanded",(_(t()),_(Tt),y(()=>t().type===Tt.folder&&t().expanded))),Qt(E,"aria-level",a()),wa(E,`padding-left: ${10*a()+20}px;`),Ae(v,(_(t()),y(()=>t().name))),Qt(b,"src",(_(t()),y(()=>i[t().inclusionState]))),Qt(b,"alt",(_(t()),y(()=>o[t().inclusionState])))},[()=>({"included-folder":n(c)})],$e),ft("click",E,l),ft("keyup",E,function(...N){var $;($=n(ce))==null||$.apply(this,N)}),d(r,oe),et()}var Li=g('<div class="files-container svelte-8hfqhl"></div>'),Oi=$t('<svg width="15" height="15" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" transform="matrix(-1 0 0 -1 16 16)" fill="currentColor" fill-opacity="0.01"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z" fill="currentColor"></path></svg>');function Zi(r){var e=Oi();d(r,e)}var Pi=g('<div class="icon-wrapper svelte-13uht7n"><!></div>'),Fi=g("<!> <!>",1),ji=g('<div class="settings-card-body"><!></div>'),Di=g('<div><div class="settings-card-content svelte-13uht7n"><div class="settings-card-left svelte-13uht7n"><!></div> <div class="settings-card-right svelte-13uht7n"><!></div></div> <!></div>');function Et(r,e){const t=Ca(e),s=us(e,["children","$$slots","$$events","$$legacy"]),a=us(s,["class","icon","title","isClickable"]);Xe(e,!1);const l=G(),i=G(),o=G();let c=w(e,"class",8,""),u=w(e,"icon",24,()=>{}),M=w(e,"title",24,()=>{}),oe=w(e,"isClickable",8,!1);_e(()=>(n(l),n(i),_(a)),()=>{m(l,a.class),m(i,ba(a,["class"]))}),_e(()=>(_(c()),n(l)),()=>{m(o,`settings-card ${c()} ${n(l)||""}`)}),ct();var E=Di();Ds(E,j=>({role:"button",class:n(o),...n(i),[$a]:j}),[()=>({clickable:oe()})],"svelte-13uht7n");var ce=h(E),ve=h(ce),A=h(ve),T=j=>{var N=Fi(),$=Ce(N),R=f=>{var k=Pi(),se=h(k);_r(se,u,(P,x)=>{x(P,{})}),d(f,k)};W($,f=>{u()&&f(R)});var B=p($,2),L=f=>{le(f,{color:"neutral",size:1,weight:"light",class:"card-title",children:(k,se)=>{var P=Z();ge(()=>Ae(P,M())),d(k,P)},$$slots:{default:!0}})};W(B,f=>{M()&&f(L)}),d(j,N)},v=j=>{var N=it(),$=Ce(N);Ct($,e,"header-left",{},null),d(j,N)};W(A,j=>{u()||M()?j(T):j(v,!1)});var J=p(ve,2),V=h(J);Ct(V,e,"header-right",{},null);var b=p(ce,2),I=j=>{var N=ji(),$=h(N);Ct($,e,"default",{},null),d(j,N)};W(b,j=>{y(()=>t.default)&&j(I)}),ft("click",E,function(j){Aa.call(this,e,j)}),d(r,E),et()}var Vi=g('<div class="context-list svelte-qsnirf"><div><!> <!></div> <div><div class="files-header svelte-qsnirf"><!> <!></div> <!></div></div>'),Ui=g('<div slot="header-right"><!></div>');function qi(r,e){Xe(e,!1);const[t,s]=Nt(),a=()=>st(i,"$wsContextModel",t),l=G();let i=new Ht(lt,new tn(lt.postMessage)),o=G(),c=G();_e(()=>a(),()=>{m(o,a().sourceFolders.sort((u,M)=>u.isWorkspaceFolder!==M.isWorkspaceFolder?u.isWorkspaceFolder?-1:1:u.fileId.folderRoot.localeCompare(M.fileId.folderRoot)))}),_e(()=>a(),()=>{m(c,a().syncStatus)}),_e(()=>n(o),()=>{m(l,n(o).reduce((u,M)=>u+(M.trackedFileCount??0),0))}),ct(),tt(),ft("message",kr,function(...u){var M;(M=i.handleMessageFromExtension)==null||M.apply(this,u)}),Et(r,{get icon(){return Zi},title:"Context",$$events:{contextmenu:u=>u.preventDefault()},children:(u,M)=>{var oe=Vi(),E=h(oe),ce=h(E);le(ce,{size:1,weight:"medium",class:"context-section-header",children:(J,V)=>{var b=Z("SOURCE FOLDERS");d(J,b)},$$slots:{default:!0}}),function(J,V){Xe(V,!1);let b=w(V,"folders",24,()=>[]),I=w(V,"onAddMore",8),j=w(V,"onRemove",8);tt();var N=Ti(),$=h(N);dt($,1,b,f=>Ht.fileIdToString(f.fileId),(f,k)=>{var se=Ai();let P;var x=h(se),ee=we=>{var De=ls(()=>Js("Enter",()=>j()(n(k).fileId.folderRoot)));ts(we,{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$events:{click:()=>j()(n(k).fileId.folderRoot),keyup(...ne){var Ne;(Ne=n(De))==null||Ne.apply(this,ne)}},children:(ne,Ne)=>{Xa(ne)},$$slots:{default:!0}})};W(x,we=>{n(k),y(()=>!n(k).isWorkspaceFolder)&&we(ee)});var Q=p(x,2);const de=$e(()=>(n(k),y(()=>(we=>we.isWorkspaceFolder?"root-folder":"folder")(n(k)))));sa(Q,{class:"source-folder-v-adjust",get icon(){return n(de)}});var me=p(Q,2),ze=h(me),Me=p(ze),S=h(Me),z=p(me,2),ye=we=>{le(we,{size:1,class:"file-count",children:(De,ne)=>{var Ne=Z();ge(Te=>Ae(Ne,Te),[()=>(n(k),y(()=>n(k).trackedFileCount.toLocaleString()))],$e),d(De,Ne)},$$slots:{default:!0}})};W(z,we=>{n(k),y(()=>n(k).trackedFileCount)&&we(ye)}),ge(we=>{P=bt(se,1,"item svelte-1skknri",null,P,we),Ae(ze,`${n(k),y(()=>n(k).name)??""} `),Ae(S,(n(k),y(()=>n(k).isPending?"(pending)":n(k).fileId.folderRoot)))},[()=>({"workspace-folder":n(k).isWorkspaceFolder})],$e),d(f,se)});var R=p($,2),B=ls(()=>Js("Enter",I())),L=h(R);hs(L,{}),ft("keyup",R,function(...f){var k;(k=n(B))==null||k.apply(this,f)}),ft("click",R,function(...f){var k;(k=I())==null||k.apply(this,f)}),d(J,N),et()}(p(ce,2),{get folders(){return n(o)},onRemove:J=>i.removeSourceFolder(J),onAddMore:()=>i.addMoreSourceFolders()});var ve=p(E,2),A=h(ve),T=h(A);le(T,{size:1,weight:"medium",class:"context-section-header",children:(J,V)=>{var b=Z("FILES");d(J,b)},$$slots:{default:!0}});var v=p(T,2);le(v,{size:1,class:"file-count",children:(J,V)=>{var b=Z();ge(I=>Ae(b,I),[()=>(n(l),y(()=>n(l).toLocaleString()))],$e),d(J,b)},$$slots:{default:!0}}),function(J,V){Xe(V,!1);const[b,I]=Nt(),j=()=>st(N(),"$wsContextModel",b);let N=w(V,"wsContextModel",8),$=G();_e(()=>j(),()=>{m($,j().sourceTree)}),ct(),tt();var R=Li();dt(R,5,()=>n($),B=>Ht.fileIdToString(B.fileId),(B,L)=>{pa(B,{get wsContextModel(){return N()},get data(){return n(L)},indentLevel:0})}),d(J,R),et(),I()}(p(A,2),{get wsContextModel(){return i}}),d(u,oe)},$$slots:{default:!0,"header-right":(u,M)=>{var oe=Ui(),E=h(oe),ce=ve=>{var A=ls(()=>Js("Enter",()=>i.requestRefresh()));ts(ve,{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$events:{click:()=>i.requestRefresh(),keyup(...T){var v;(v=n(A))==null||v.apply(this,T)}},children:(T,v)=>{Fa(T)},$$slots:{default:!0}})};W(E,ve=>{n(c),_(gr),y(()=>n(c)===gr.done)&&ve(ce)}),d(u,oe)}}}),et(),s()}function Sr(r){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(r)&&"name"in r}function Br(r){return Sr(r)&&"component"in r}var Hi=$t('<svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z" fill="currentColor"></path></svg>');function Gr(r){var e=Hi();d(r,e)}var Bi=g('<div class="c-navigation__content-header svelte-z0ijuz"> </div>'),Gi=g('<div class="c-navigation__content-description svelte-z0ijuz"> </div>'),Ji=g('<!> <!> <div class="c-navigation__content-container svelte-z0ijuz"><!></div>',1),Wi=g('<div class="c-navigation__content svelte-z0ijuz"><!> <div><!></div></div>');function Jr(r,e){Xe(e,!1);let t=w(e,"item",8);tt();var s=Wi(),a=h(s);Ct(a,e,"header",{},null);var l=p(a,2),i=h(l),o=c=>{var u=Ji(),M=Ce(u);le(M,{size:4,weight:"medium",color:"neutral",children:(A,T)=>{var v=Bi(),J=h(v);ge(()=>Ae(J,(_(t()),y(()=>{var V;return(V=t())==null?void 0:V.name})))),d(A,v)},$$slots:{default:!0}});var oe=p(M,2),E=A=>{le(A,{color:"secondary",size:1,weight:"light",children:(T,v)=>{var J=Gi(),V=h(J);ge(()=>Ae(V,(_(t()),y(()=>{var b;return(b=t())==null?void 0:b.description})))),d(T,J)},$$slots:{default:!0}})};W(oe,A=>{_(t()),y(()=>{var T;return(T=t())==null?void 0:T.description})&&A(E)});var ce=p(oe,2),ve=h(ce);Ct(ve,e,"content",{get item(){return t()}},null),d(c,u)};W(i,c=>{t()!=null&&c(o)}),ge(()=>Qt(s,"id",(_(t()),y(()=>{var c;return(c=t())==null?void 0:c.id})))),d(r,s),et()}function Cs(r,e,t,s,a,l){return a!==void 0?{name:r,description:e,icon:t,id:s,component:a,props:l}:{name:r,description:e,icon:t,id:s}}var Ki=g('<div class="c-navigation__head svelte-n5ccbo"><!> <!></div>'),Yi=g('<span class="c-navigation__head-icon"><!></span> ',1),Qi=g("<button><!></button>"),Xi=g('<div class="c-navigation__group"><!> <div class="c-navigation__items svelte-n5ccbo"></div></div>'),eo=g('<nav class="c-navigation__nav svelte-n5ccbo" slot="left"><!></nav>'),to=g('<span class="c-navigation__head-icon"><!></span> <span> </span>',1),so=g("<span><!></span>"),ro=g('<div class="c-navigation__head svelte-n5ccbo"><!></div> <!>',1),ao=g('<div class="c-navigation__flat svelte-n5ccbo"><!> <!></div>'),no=g("<div><!></div>");function io(r,e){Xe(e,!1);let t=w(e,"group",8,"Workspace Settings"),s=w(e,"items",24,()=>[]),a=w(e,"item",28,()=>{}),l=w(e,"mode",8,"tree"),i=w(e,"selectedId",28,()=>{}),o=w(e,"onNavigationChangeItem",8,A=>{}),c=w(e,"showButton",8,!0),u=w(e,"class",8,""),M=G(new Map);_e(()=>(_(i()),_(a()),_(s())),()=>{var A;i()?a(s().find(T=>(T==null?void 0:T.id)===i())):i((A=a())==null?void 0:A.id)}),_e(()=>(_(s()),_(t())),()=>{m(M,s().reduce((A,T)=>{if(!T)return A;const v=T.group??t(),J=A.get(v)??[];return J.push(T),A.set(v,J),A},new Map))}),_e(()=>(_(a()),_(s())),()=>{a()||a(s()[0])}),_e(()=>(_(o()),_(i())),()=>{o()(i())}),ct(),tt();var oe=no(),E=h(oe),ce=A=>{an(A,{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,get showButton(){return c()},minimized:!1,$$slots:{left:(T,v)=>{var J=eo(),V=h(J);sn(V,i,b=>{var I=it(),j=Ce(I);dt(j,1,()=>n(M),qt,(N,$)=>{var R=ls(()=>Er(n($),2));let B=()=>n(R)[0];var L=Xi(),f=h(L);Ct(f,e,"group",{get label(){return B()},get mode(){return l()}},se=>{var P=Ki(),x=h(P);Gr(x);var ee=p(x,2);le(ee,{size:2,color:"primary",children:(Q,de)=>{var me=Z();ge(()=>Ae(me,B())),d(Q,me)},$$slots:{default:!0}}),d(se,P)});var k=p(f,2);dt(k,5,()=>n(R)[1],qt,(se,P)=>{var x=Qi();let ee;var Q=h(x);le(Q,{size:2,weight:"regular",color:"primary",children:(de,me)=>{var ze=Yi(),Me=Ce(ze),S=h(Me);_r(S,()=>n(P).icon,(ye,we)=>{we(ye,{})});var z=p(Me);ge(()=>Ae(z,` ${n(P),y(()=>n(P).name)??""}`)),d(de,ze)},$$slots:{default:!0}}),ge(de=>ee=bt(x,1,"c-navigation__item svelte-n5ccbo",null,ee,de),[()=>({"is-active":n(P).id===i()})],$e),ft("click",x,()=>{return de=n(P),a(de),void i(de==null?void 0:de.id);var de}),d(se,x)}),d(N,L)}),d(b,I)}),d(T,J)},right:(T,v)=>{Jr(T,{get item(){return a()},slot:"right",$$slots:{header:(J,V)=>{var b=it(),I=Ce(b);Ct(I,e,"header",{get item(){return a()},get selectedId(){return i()}},null),d(J,b)},content:(J,V)=>{var b=it(),I=Ce(b);Ct(I,e,"content",{get item(){return a()},get isSelected(){return _(a()),_(i()),y(()=>{var j;return((j=a())==null?void 0:j.id)===i()})}},j=>{var N=it(),$=Ce(N),R=B=>{var L=it(),f=Ce(L);_r(f,()=>a().component,(k,se)=>{se(k,Sa(()=>a().props))}),d(B,L)};W($,B=>{_(Br),_(a()),_(l()),_(i()),y(()=>{return Br(a())&&(L=a(),f=l(),k=i(),f!=="tree"||(L==null?void 0:L.id)===k);var L,f,k})&&B(R)}),d(j,N)}),d(J,b)}}})}}})},ve=A=>{var T=ao(),v=h(T);Ct(v,e,"header",{get item(){return a()}},null);var J=p(v,2);dt(J,1,()=>n(M),qt,(V,b)=>{var I=ls(()=>Er(n(b),2));let j=()=>n(I)[0];var N=ro(),$=Ce(N),R=h($);Ct(R,e,"group",{get label(){return j()},get mode(){return l()}},L=>{le(L,{color:"secondary",size:2,weight:"medium",children:(f,k)=>{var se=to(),P=Ce(se);Gr(h(P));var x=p(P,2),ee=h(x);ge(()=>Ae(ee,j())),d(f,se)},$$slots:{default:!0}})});var B=p($,2);dt(B,1,()=>n(I)[1],qt,(L,f)=>{var k=so();Jr(h(k),{get item(){return n(f)},$$slots:{content:(se,P)=>{var x=it(),ee=Ce(x);Ct(ee,e,"content",{get item(){return n(f)}},null),d(se,x)}}}),Ta(k,(se,P)=>function(x,ee){let Q;function de({scrollTo:me,delay:ze,options:Me}){clearTimeout(Q),me&&(Q=setTimeout(()=>{x.scrollIntoView(Me)},ze))}return de(ee),{update:de,destroy(){clearTimeout(Q)}}}(se,P),()=>({scrollTo:l()==="flat"&&n(f).id===i(),delay:300,options:{behavior:"smooth"}})),d(L,k)}),d(V,N)}),d(A,T)};W(E,A=>{l()==="tree"?A(ce):A(ve,!1)}),ge(()=>bt(oe,1,`c-navigation c-navigation--mode__${l()??""} ${u()??""}`,"svelte-n5ccbo")),d(r,oe),et()}var oo=$t('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z" fill="currentColor"></path></svg>');function lo(r){var e=oo();d(r,e)}var co=$t('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z" fill="currentColor"></path></svg>');function uo(r){var e=co();d(r,e)}var ho=$t("<svg><!></svg>");function va(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=ho();Ds(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 17 16",...t}));var a=h(s);nr(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.57.57 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.57.57 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.57.57 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.57.57 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.57.57 0 0 1-.06-.734zm3.759-3.759a.57.57 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.57.57 0 0 1-.804 0L7.31 4.204a.57.57 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',!0),d(r,s)}var po=g('<div class="connect-button-spinner svelte-js5lik"><!></div> <span>Cancel</span>',1),vo=g("<span>Connect</span>"),mo=g('<div class="connect-button-content svelte-js5lik"><!></div>'),go=g('<div class="status-controls svelte-js5lik"><div class="icon-container svelte-js5lik"><div class="connection-status svelte-js5lik"><div><!></div></div> <!></div></div>'),fo=g('<div slot="header-right"><!></div>'),yo=g("<div> </div>"),_o=g('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),wo=g('<div class="loading-container svelte-2bsejd"><!> <!></div>'),Co=g('<div class="category-content"><!></div>'),bo=g('<div class="category"><div class="category-heading"><!></div> <!></div>');const ma="extensionClient",ga="mcpServerModel";function Nr(){const r=ds(ga);if(!r)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return r}var $o=g('<div class="connect-button-spinner svelte-e3a21z"><!></div> <span>Cancel</span>',1),So=g("<span>Connect</span>"),ko=g('<div class="connect-button-content svelte-e3a21z"><!></div>'),xo=g('<div class="status-controls svelte-e3a21z"><div><!></div> <!></div>'),Mo=g('<div slot="header-right"><!></div>'),Ao=g("<div> </div>"),To=g('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>');function No(r,e){Xe(e,!1);let t=w(e,"config",12),s=w(e,"mcpTool",8);const a=Nr(),l=function(){const A=ds(ma);if(!A)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return A}();async function i(){if(n(c))return M&&(clearTimeout(M),M=null),void m(c,!1);l.startRemoteMCPAuth(t().name),m(c,!0);const A=new Promise(T=>{M=setTimeout(()=>{T(),M=null},6e4)});await Promise.race([A]),m(c,!1)}async function o(){var A;await l.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${t().displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(s()&&a.deleteServer((A=s())==null?void 0:A.id),m(c,!1))}let c=G(!1),u=G(!1),M=null;_e(()=>(_(t()),Zr),()=>{t(Zr(t()))}),_e(()=>_(s()),()=>{t(t().isConfigured=!!s(),!0)}),ct(),tt();var oe=To(),E=h(oe);Et(E,{get icon(){return _(t()),y(()=>t().icon)},get title(){return _(t()),y(()=>t().displayName)},$$slots:{"header-right":(A,T)=>{var v=Mo(),J=h(v),V=I=>{const j=$e(()=>n(c)?"neutral":"accent");Qe(I,{variant:"ghost-block",get color(){return n(j)},size:1,$$events:{click:i},children:(N,$)=>{var R=ko(),B=h(R),L=k=>{var se=$o(),P=Ce(se),x=h(P);Ks(x,{size:1,useCurrentColor:!0}),d(k,se)},f=k=>{var se=So();d(k,se)};W(B,k=>{n(c)?k(L):k(f,!1)}),d(N,R)},$$slots:{default:!0}})},b=(I,j)=>{var N=$=>{var R=xo(),B=h(R);let L;var f=h(B);const k=$e(()=>(_(ps),y(()=>[ps.Hover])));Ft(f,{get triggerOn(){return n(k)},content:"Revoke Access",children:(P,x)=>{ts(P,{color:"neutral",variant:"ghost",size:1,$$events:{click:o},children:(ee,Q)=>{va(ee,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var se=p(B,2);Mr.Root(se,{color:"success",size:1,variant:"soft",children:(P,x)=>{var ee=Z("Connected");d(P,ee)},$$slots:{default:!0}}),ge(P=>L=bt(B,1,"disconnect-button svelte-e3a21z",null,L,P),[()=>({active:n(u)})],$e),d($,R)};W(I,$=>{_(t()),y(()=>t().isConfigured)&&$(N)},j)};W(J,I=>{_(t()),y(()=>!t().isConfigured)?I(V):I(b,!1)}),d(A,v)}}});var ce=p(E,2),ve=A=>{var T=Ao(),v=h(T);ge(()=>{bt(T,1,`status-message ${_(t()),y(()=>t().statusType)??""}`,"svelte-e3a21z"),Ae(v,(_(t()),y(()=>t().statusMessage)))}),d(A,T)};W(ce,A=>{_(t()),y(()=>t().showStatus)&&A(ve)}),ft("mouseenter",oe,()=>m(u,!0)),ft("mouseleave",oe,()=>m(u,!1)),d(r,oe),et()}var Ro=g('<div class="tool-category-list svelte-on3wl5"><!> <!></div>'),Eo=g("<div><!></div>");function Io(r,e){Xe(e,!1);const[t,s]=Nt(),a=()=>st(A,"$allServers",t),l=()=>st(ve,"$pretendNativeToolDefs",t),i=G();let o=w(e,"title",8),c=w(e,"tools",24,()=>[]),u=w(e,"onAuthenticate",8),M=w(e,"onRevokeAccess",8),oe=w(e,"onToolApprovalConfigChange",8,()=>{});const E=ds(Ms.key),ce=Nr(),ve=E.getPretendNativeToolDefs(),A=ce.getServers();_e(()=>a(),()=>{m(i,E.getEnableNativeRemoteMcp()?Yr(a()):[])}),ct(),tt();var T=Eo(),v=h(T);const J=$e(()=>(_(c()),y(()=>c().length===0)));(function(V,b){let I=w(b,"title",8),j=w(b,"loading",8,!1);var N=bo(),$=h(N),R=h($);le(R,{size:1,color:"secondary",weight:"regular",children:(k,se)=>{var P=Z();ge(()=>Ae(P,I())),d(k,P)},$$slots:{default:!0}});var B=p($,2),L=k=>{var se=wo(),P=h(se);Ks(P,{size:1});var x=p(P,2);le(x,{size:1,color:"secondary",children:(ee,Q)=>{var de=Z("Loading...");d(ee,de)},$$slots:{default:!0}}),d(k,se)},f=k=>{var se=Co(),P=h(se);Ct(P,b,"default",{},null),d(k,se)};W(B,k=>{j()?k(L):k(f,!1)}),d(V,N)})(v,{get title(){return o()},get loading(){return n(J)},children:(V,b)=>{var I=Ro(),j=h(I);dt(j,1,c,$=>$.name,($,R)=>{(function(B,L){Xe(L,!1);let f=w(L,"config",8),k=w(L,"onAuthenticate",8),se=w(L,"onRevokeAccess",8);const P=()=>{};let x=G(!1),ee=G(null),Q=G(!1);function de(){if(n(x))m(x,!1),n(ee)&&(clearTimeout(n(ee)),m(ee,null));else{m(x,!0);const z=f().authUrl||"";k()(z),m(ee,setTimeout(()=>{m(x,!1),m(ee,null)},6e4))}}_e(()=>(_(f()),n(x),n(ee)),()=>{f().isConfigured&&n(x)&&(m(x,!1),n(ee)&&(clearTimeout(n(ee)),m(ee,null)))}),ct(),tt();var me=_o(),ze=h(me);Et(ze,{get icon(){return _(f()),y(()=>f().icon)},get title(){return _(f()),y(()=>f().displayName)},$$slots:{"header-right":(z,ye)=>{var we=fo(),De=h(we),ne=Te=>{const Se=$e(()=>n(x)?"neutral":"accent");Qe(Te,{variant:"ghost-block",get color(){return n(Se)},size:1,$$events:{click:de},children:(Ee,be)=>{var ue=mo(),qe=h(ue),O=F=>{var X=po(),ie=Ce(X),te=h(ie);Ks(te,{size:1,useCurrentColor:!0}),d(F,X)},U=F=>{var X=vo();d(F,X)};W(qe,F=>{n(x)?F(O):F(U,!1)}),d(Ee,ue)},$$slots:{default:!0}})},Ne=(Te,Se)=>{var Ee=be=>{var ue=go(),qe=h(ue),O=h(qe),U=h(O);let F;var X=h(U);const ie=$e(()=>(_(ps),y(()=>[ps.Hover])));Ft(X,{get triggerOn(){return n(ie)},content:"Revoke Access",children:(he,D)=>{ts(he,{color:"neutral",variant:"ghost",size:1,$$events:{click:()=>se()(f())},children:(q,re)=>{va(q,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var te=p(O,2);Mr.Root(te,{color:"success",size:1,variant:"soft",children:(he,D)=>{var q=Z("Connected");d(he,q)},$$slots:{default:!0}}),ge(he=>F=bt(U,1,"icon-button-wrapper svelte-js5lik",null,F,he),[()=>({active:n(Q)})],$e),d(be,ue)};W(Te,be=>{_(f()),y(()=>f().isConfigured)&&be(Ee)},Se)};W(De,Te=>{_(f()),y(()=>!f().isConfigured&&f().authUrl)?Te(ne):Te(Ne,!1)}),d(z,we)}}});var Me=p(ze,2),S=z=>{var ye=yo(),we=h(ye);ge(()=>{bt(ye,1,`status-message ${_(f()),y(()=>f().statusType)??""}`,"svelte-js5lik"),Ae(we,(_(f()),y(()=>f().statusMessage)))}),d(z,ye)};W(Me,z=>{_(f()),y(()=>f().showStatus)&&z(S)}),ft("mouseenter",me,()=>m(Q,!0)),ft("mouseleave",me,()=>m(Q,!1)),d(B,me),aa(L,"onToolApprovalConfigChange",P),et({onToolApprovalConfigChange:P})})($,{get config(){return n(R)},get onAuthenticate(){return u()},get onRevokeAccess(){return M()},onToolApprovalConfigChange:oe()})});var N=p(j,2);dt(N,1,l,$=>$.name,($,R)=>{const B=$e(()=>(n(i),n(R),y(()=>n(i).find(L=>L.name===n(R).name))));No($,{get mcpTool(){return n(B)},get config(){return n(R)}})}),d(V,I)},$$slots:{default:!0}}),d(r,T),et(),s()}var zo=g('<tr class="env-var-row svelte-1mazg1z"><td class="name-cell svelte-1mazg1z"><!></td><td class="value-cell svelte-1mazg1z"><!></td><td class="action-cell svelte-1mazg1z"><!></td></tr>'),Lo=g('<!> <table class="env-vars-table svelte-1mazg1z"><tbody><!></tbody></table> <div class="new-var-button-container svelte-1mazg1z"><!></div>',1);function Oo(r,e){Xe(e,!1);let t=w(e,"handleEnterEditMode",8),s=w(e,"envVarEntries",28,()=>[]);tt();var a=Lo(),l=Ce(a);le(l,{size:1,weight:"medium",children:(E,ce)=>{var ve=Z("Environment Variables");d(E,ve)},$$slots:{default:!0}});var i=p(l,2),o=h(i),c=h(o),u=E=>{var ce=it(),ve=Ce(ce);dt(ve,1,s,A=>A.id,(A,T,v)=>{var J=zo(),V=h(J),b=h(V);Xt(b,{size:1,placeholder:"Name",class:"full-width",get value(){return n(T).key},set value(R){n(T).key=R,Ir(()=>s())},$$events:{focus(...R){var B;(B=t())==null||B.apply(this,R)},change:()=>function(R,B){const L=s().findIndex(f=>f.id===R);L!==-1&&(s(s()[L].key=B,!0),s(s()))}(n(T).id,n(T).key)},$$legacy:!0});var I=p(V),j=h(I);Xt(j,{size:1,placeholder:"Value",class:"full-width",get value(){return n(T).value},set value(R){n(T).value=R,Ir(()=>s())},$$events:{focus(...R){var B;(B=t())==null||B.apply(this,R)},change:()=>function(R,B){const L=s().findIndex(f=>f.id===R);L!==-1&&(s(s()[L].value=B,!0),s(s()))}(n(T).id,n(T).value)},$$legacy:!0});var N=p(I),$=h(N);Ft($,{content:"Remove",children:(R,B)=>{Qe(R,{variant:"ghost",color:"neutral",type:"button",size:1,$$events:{focus(...L){var f;(f=t())==null||f.apply(this,L)},click:()=>{return L=n(T).id,t()(),void s(s().filter(f=>f.id!==L));var L}},$$slots:{iconLeft:(L,f)=>{Qr(L,{slot:"iconLeft"})}}})},$$slots:{default:!0}}),d(A,J)}),d(E,ce)};W(c,E=>{_(s()),y(()=>s().length>0)&&E(u)});var M=p(i,2),oe=h(M);Qe(oe,{size:1,variant:"soft",color:"neutral",type:"button",$$events:{click:function(){t()(),s([...s(),{id:crypto.randomUUID(),key:"",value:""}])}},children:(E,ce)=>{var ve=Z("Variable");d(E,ve)},$$slots:{default:!0,iconLeft:(E,ce)=>{hs(E,{slot:"iconLeft"})}}}),d(r,a),et()}var Zo=g("<div></div>"),Po=g(" <!>",1),Fo=g('<div class="server-name svelte-igdbzh"><!></div>'),jo=g('<div slot="header-left" class="l-header svelte-igdbzh"><!> <!> <!> <div class="command-text svelte-igdbzh"><!></div></div>'),Do=g('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),Vo=g('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),Uo=g('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),qo=g("<!> <!> <!>",1),Ho=g("<!> <!>",1),Bo=g('<div class="server-actions svelte-igdbzh" slot="header-right"><div class="status-controls svelte-igdbzh"><!> <!></div></div>'),Go=g('<div class="c-tool-item svelte-igdbzh"><div class="c-tool-info svelte-igdbzh"><div class="tool-status svelte-igdbzh"><div></div> <!></div> <div class="c-tool-description svelte-igdbzh"><!></div></div></div>'),Jo=g('<div slot="footer"></div>'),Wo=g('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div> <div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>',1),Ko=g('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!> <div class="connection-type-buttons svelte-igdbzh"><!> <!></div></div></div>'),Yo=g('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>'),Qo=g('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>'),Xo=g('<!> <div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div> <!>',1),el=g('<form><div class="server-edit-form svelte-igdbzh"><div class="server-header svelte-igdbzh"><div class="server-title svelte-igdbzh"><div class="server-icon svelte-igdbzh"><!></div> <!></div></div> <!> <!> <div class="form-actions-row svelte-igdbzh"><div><!></div> <div class="form-actions svelte-igdbzh"><!> <!></div></div></div></form>');function Wr(r,e){var Me;Xe(e,!1);const t=G(),s=G(),a=G(),l=G(),i=G();let o=w(e,"server",8,null),c=w(e,"onDelete",8),u=w(e,"onAdd",8),M=w(e,"onSave",8),oe=w(e,"onEdit",8),E=w(e,"onToggleDisableServer",8),ce=w(e,"onJSONImport",8),ve=w(e,"onCancel",8),A=w(e,"disabledText",24,()=>{}),T=w(e,"warningText",24,()=>{}),v=w(e,"mode",12,"view"),J=w(e,"mcpServerError",12,""),V=G(((Me=o())==null?void 0:Me.name)??""),b=G(kt(o())?"":os(o())?o().command:""),I=G(kt(o())?o().url:""),j=os(o())?o().env??{}:{},N=G(""),$=G(kt(o())?o().type:"http"),R=G([]);L();let B=G(!0);function L(){m(R,Object.entries(j).map(([S,z])=>({id:crypto.randomUUID(),key:S,value:z})))}let f=G(()=>{});function k(){o()&&v()==="view"&&(v("edit"),oe()(o()),n(f)())}let se=w(e,"busy",12,!1);function P({key:S,value:z}){return S.trim()&&z.trim()}async function x(){J(""),se(!0);const S=n(R).filter(P);j=Object.fromEntries(S.map(({key:z,value:ye})=>[z.trim(),ye.trim()])),L();try{if(v()==="add"){const z={type:"stdio",name:n(V).trim(),command:n(b).trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(j).length>0?j:void 0};await u()(z)}else if(v()==="addRemote"){const z={type:n($),name:n(V).trim(),url:n(I).trim()};await u()(z)}else if(v()==="addJson"){try{JSON.parse(n(N))}catch(z){const ye=z instanceof Error?z.message:String(z);throw new pt(`Invalid JSON format: ${ye}`)}await ce()(n(N))}else if(v()==="edit"&&o()){if(kt(o())){const z={...o(),type:n($),name:n(V).trim(),url:n(I).trim()};await M()(z)}else if(os(o())){const z={...o(),name:n(V).trim(),command:n(b).trim(),arguments:"",env:Object.keys(j).length>0?j:void 0};await M()(z)}}}catch(z){J(z instanceof pt?z.message:"Failed to save server"),console.warn(z)}finally{se(!1)}}function ee(){var S,z;se(!1),J(""),(S=ve())==null||S(),m(N,""),m(V,((z=o())==null?void 0:z.name)??""),m(b,kt(o())?"":os(o())?o().command:""),m(I,kt(o())?o().url:""),j=os(o())&&o().env?{...o().env}:{},m($,kt(o())?o().type:"http"),L()}_e(()=>_(o()),()=>{var S;m(t,((S=o())==null?void 0:S.tools)??[])}),_e(()=>(n(V),n(b)),()=>{n(V)&&n(b)&&J("")}),_e(()=>(_(v()),n(V),n(b),n(I)),()=>{m(s,!((v()!=="add"||n(V).trim()&&n(b).trim())&&(v()!=="addRemote"||n(V).trim()&&n(I).trim())))}),_e(()=>(_(v()),n(N)),()=>{m(a,v()==="addJson"&&!n(N).trim())}),_e(()=>(n(s),_(v()),n(a)),()=>{m(l,n(s)||v()==="view"||n(a))}),_e(()=>_(v()),()=>{m(i,(()=>{switch(v()){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())}),ct(),tt();var Q=it(),de=Ce(Q),me=S=>{ra(S,{get collapsed(){return n(B)},set collapsed(z){m(B,z)},$$slots:{header:(z,ye)=>{Et(z,{slot:"header",$$slots:{"header-left":(we,De)=>{var ne=jo(),Ne=h(ne),Te=U=>{ts(U,{size:1,variant:"ghost",$$events:{click:()=>m(B,!n(B))},children:(F,X)=>{var ie=it(),te=Ce(ie),he=q=>{Ja(q,{})},D=q=>{Ar(q,{})};W(te,q=>{n(B)?q(he):q(D,!1)}),d(F,ie)},$$slots:{default:!0}})};W(Ne,U=>{n(t),y(()=>n(t).length>0)&&U(Te)});var Se=p(Ne,2);const Ee=$e(()=>A()||T());Ft(Se,{get content(){return n(Ee)},children:(U,F)=>{var X=Zo();let ie;ge(te=>ie=bt(X,1,"c-dot svelte-igdbzh",null,ie,te),[()=>({"c-green":!A(),"c-warning":!A()&&!!T(),"c-red":!!A(),"c-disabled":o().disabled})],$e),d(U,X)},$$slots:{default:!0}});var be=p(Se,2);Ft(be,{get content(){return _(o()),y(()=>o().name)},side:"top",align:"start",children:(U,F)=>{var X=Fo(),ie=h(X);le(ie,{size:1,weight:"medium",children:(te,he)=>{var D=Po(),q=Ce(D),re=p(q),Re=K=>{var Ve=Z();ge(()=>Ae(Ve,`(${n(t),y(()=>n(t).length)??""}) tools`)),d(K,Ve)};W(re,K=>{n(t),y(()=>n(t).length>0)&&K(Re)}),ge(()=>Ae(q,`${_(o()),y(()=>o().name)??""} `)),d(te,D)},$$slots:{default:!0}}),d(U,X)},$$slots:{default:!0}});var ue=p(be,2),qe=h(ue);const O=$e(()=>(_(Gs),_(o()),y(()=>Gs(o()))));Ft(qe,{get content(){return n(O)},side:"top",align:"start",children:(U,F)=>{le(U,{color:"secondary",size:1,weight:"regular",children:(X,ie)=>{var te=Z();ge(he=>Ae(te,he),[()=>(_(Gs),_(o()),y(()=>Gs(o())))],$e),d(X,te)},$$slots:{default:!0}})},$$slots:{default:!0}}),d(we,ne)},"header-right":(we,De)=>{var ne=Bo(),Ne=h(ne),Te=h(Ne),Se=be=>{const ue=$e(()=>(_(o()),y(()=>!o().disabled)));fr(be,{size:1,get checked(){return n(ue)},$$events:{change:()=>{o()&&E()(o().id),n(f)()}}})};W(Te,be=>{_(Lr),y(Lr)&&be(Se)});var Ee=p(Te,2);Ye.Root(Ee,{get requestClose(){return n(f)},set requestClose(be){m(f,be)},children:(be,ue)=>{var qe=Ho(),O=Ce(qe);Ye.Trigger(O,{children:(F,X)=>{ts(F,{size:1,variant:"ghost-block",color:"neutral",children:(ie,te)=>{on(ie,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var U=p(O,2);Ye.Content(U,{side:"bottom",align:"end",children:(F,X)=>{var ie=qo(),te=Ce(ie);Ye.Item(te,{onSelect:k,children:(q,re)=>{var Re=Do(),K=h(Re);ln(K,{});var Ve=p(K,2);le(Ve,{size:1,weight:"medium",children:(Le,Je)=>{var Ue=Z("Edit");d(Le,Ue)},$$slots:{default:!0}}),d(q,Re)},$$slots:{default:!0}});var he=p(te,2);Ye.Item(he,{onSelect:()=>{(function(){if(o()){const q=Us.convertServerToJSON(o());navigator.clipboard.writeText(q)}})(),n(f)()},children:(q,re)=>{var Re=Vo(),K=h(Re);cn(K,{});var Ve=p(K,2);le(Ve,{size:1,weight:"medium",children:(Le,Je)=>{var Ue=Z("Copy JSON");d(Le,Ue)},$$slots:{default:!0}}),d(q,Re)},$$slots:{default:!0}});var D=p(he,2);Ye.Item(D,{color:"error",onSelect:()=>{c()(o().id),n(f)()},children:(q,re)=>{var Re=Uo(),K=h(Re);Qr(K,{});var Ve=p(K,2);le(Ve,{size:1,weight:"medium",children:(Le,Je)=>{var Ue=Z("Delete");d(Le,Ue)},$$slots:{default:!0}}),d(q,Re)},$$slots:{default:!0}}),d(F,ie)},$$slots:{default:!0}}),d(be,qe)},$$slots:{default:!0},$$legacy:!0}),d(we,ne)}}})},footer:(z,ye)=>{var we=Jo();dt(we,5,()=>n(t),qt,(De,ne)=>{var Ne=Go(),Te=h(Ne),Se=h(Te),Ee=h(Se);let be;var ue=p(Ee,2);le(ue,{size:1,weight:"medium",children:(U,F)=>{var X=Z();ge(()=>Ae(X,(n(ne),y(()=>n(ne).definition.mcp_tool_name||n(ne).definition.name)))),d(U,X)},$$slots:{default:!0}});var qe=p(Se,2),O=h(qe);Ft(O,{get content(){return n(ne),y(()=>n(ne).definition.description)},align:"start",children:(U,F)=>{var X=it(),ie=Ce(X),te=he=>{le(he,{size:1,color:"secondary",children:(D,q)=>{var re=Z();ge(()=>Ae(re,(n(ne),y(()=>n(ne).definition.description)))),d(D,re)},$$slots:{default:!0}})};W(ie,he=>{n(ne),y(()=>n(ne).definition.description)&&he(te)}),d(U,X)},$$slots:{default:!0}}),ge(U=>be=bt(Ee,1,"tool-status-dot svelte-igdbzh",null,be,U),[()=>({enabled:n(ne).enabled,disabled:!n(ne).enabled})],$e),d(De,Ne)}),d(z,we)}},$$legacy:!0})},ze=S=>{var z=el(),ye=h(z),we=h(ye),De=h(we),ne=h(De),Ne=h(ne);ja(Ne);var Te=p(ne,2);le(Te,{color:"secondary",size:1,weight:"medium",children:(D,q)=>{var re=Z();ge(()=>Ae(re,n(i))),d(D,re)},$$slots:{default:!0}});var Se=p(we,2),Ee=D=>{var q=Wo(),re=Ce(q),Re=h(re),K=h(Re);le(K,{size:1,weight:"medium",children:(Ue,mt)=>{var Oe=Z("Code Snippet");d(Ue,Oe)},$$slots:{default:!0}});var Ve=p(re,2),Le=h(Ve),Je=h(Le);na(Je,{size:1,placeholder:"Paste JSON here...",get value(){return n(N)},set value(Ue){m(N,Ue)},$$legacy:!0}),d(D,q)},be=(D,q)=>{var re=Re=>{var K=Xo(),Ve=Ce(K),Le=We=>{var He=Ko(),nt=h(He),St=h(nt);le(St,{size:1,weight:"medium",children:(Ot,qs)=>{var ys=Z("Connection Type");d(Ot,ys)},$$slots:{default:!0}});var ot=p(St,2),Lt=h(ot);const Kt=$e(()=>n($)==="http"?"solid":"ghost"),fs=$e(()=>n($)==="http"?"accent":"neutral");Qe(Lt,{size:1,get variant(){return n(Kt)},get color(){return n(fs)},type:"button",$$events:{click:()=>m($,"http")},children:(Ot,qs)=>{var ys=Z("HTTP");d(Ot,ys)},$$slots:{default:!0}});var Ze=p(Lt,2);const rt=$e(()=>n($)==="sse"?"solid":"ghost"),yt=$e(()=>n($)==="sse"?"accent":"neutral");Qe(Ze,{size:1,get variant(){return n(rt)},get color(){return n(yt)},type:"button",$$events:{click:()=>m($,"sse")},children:(Ot,qs)=>{var ys=Z("SSE");d(Ot,ys)},$$slots:{default:!0}}),d(We,He)};W(Ve,We=>{_(v()),_(o()),y(()=>{var He,nt;return v()==="addRemote"||v()==="edit"&&(((He=o())==null?void 0:He.type)==="http"||((nt=o())==null?void 0:nt.type)==="sse")})&&We(Le)});var Je=p(Ve,2),Ue=h(Je),mt=h(Ue);Xt(mt,{size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",get value(){return n(V)},set value(We){m(V,We)},$$events:{focus:k},$$slots:{label:(We,He)=>{le(We,{slot:"label",size:1,weight:"medium",children:(nt,St)=>{var ot=Z("Name");d(nt,ot)},$$slots:{default:!0}})}},$$legacy:!0});var Oe=p(Je,2),Fe=We=>{var He=Yo(),nt=h(He),St=h(nt);Xt(St,{size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",get value(){return n(I)},set value(ot){m(I,ot)},$$events:{focus:k},$$slots:{label:(ot,Lt)=>{le(ot,{slot:"label",size:1,weight:"medium",children:(Kt,fs)=>{var Ze=Z("URL");d(Kt,Ze)},$$slots:{default:!0}})}},$$legacy:!0}),d(We,He)},at=We=>{var He=Qo(),nt=h(He),St=h(nt);Xt(St,{size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",get value(){return n(b)},set value(ot){m(b,ot)},$$events:{focus:k},$$slots:{label:(ot,Lt)=>{le(ot,{slot:"label",size:1,weight:"medium",children:(Kt,fs)=>{var Ze=Z("Command");d(Kt,Ze)},$$slots:{default:!0}})}},$$legacy:!0}),d(We,He)};W(Oe,We=>{_(v()),_(o()),y(()=>{var He,nt;return v()==="addRemote"||((He=o())==null?void 0:He.type)==="http"||((nt=o())==null?void 0:nt.type)==="sse"})?We(Fe):We(at,!1)}),d(Re,K)};W(D,Re=>{v()!=="add"&&v()!=="addRemote"&&v()!=="edit"||Re(re)},q)};W(Se,D=>{v()==="addJson"?D(Ee):D(be,!1)});var ue=p(Se,2),qe=D=>{Oo(D,{handleEnterEditMode:k,get envVarEntries(){return n(R)},set envVarEntries(q){m(R,q)},$$legacy:!0})};W(ue,D=>{_(v()),_(kt),_(o()),y(()=>(v()==="add"||v()==="edit")&&!kt(o()))&&D(qe)});var O=p(ue,2),U=h(O);let F;var X=h(U);xs(X,{variant:"soft",color:"error",size:1,children:(D,q)=>{var re=Z();ge(()=>Ae(re,J())),d(D,re)},$$slots:{default:!0,icon:(D,q)=>{Da(D,{slot:"icon"})}}});var ie=p(U,2),te=h(ie);Qe(te,{size:1,variant:"ghost",color:"neutral",type:"button",$$events:{click:ee},children:(D,q)=>{var re=Z("Cancel");d(D,re)},$$slots:{default:!0}});var he=p(te,2);Qe(he,{size:1,variant:"solid",color:"accent",get loading(){return se()},type:"submit",get disabled(){return n(l)},children:(D,q)=>{var re=it(),Re=Ce(re),K=Le=>{var Je=Z("Import");d(Le,Je)},Ve=(Le,Je)=>{var Ue=Oe=>{var Fe=Z("Add");d(Oe,Fe)},mt=(Oe,Fe)=>{var at=He=>{var nt=Z("Add");d(He,nt)},We=(He,nt)=>{var St=ot=>{var Lt=Z("Save");d(ot,Lt)};W(He,ot=>{v()==="edit"&&ot(St)},nt)};W(Oe,He=>{v()==="addRemote"?He(at):He(We,!1)},Fe)};W(Le,Oe=>{v()==="add"?Oe(Ue):Oe(mt,!1)},Je)};W(Re,Le=>{v()==="addJson"?Le(K):Le(Ve,!1)}),d(D,re)},$$slots:{default:!0}}),ge(D=>{bt(z,1,"c-mcp-server-card "+(v()==="add"||v()==="addJson"||v()==="addRemote"?"add-server-section":"server-item"),"svelte-igdbzh"),F=bt(U,1,"error-container svelte-igdbzh",null,F,D)},[()=>({"is-error":!!J()})],$e),ft("submit",z,nn(x)),d(S,z)};return W(de,S=>{v()==="view"&&o()?S(me):S(ze,!1)}),d(r,Q),aa(e,"setLocalEnvVarFormState",L),et({setLocalEnvVarFormState:L})}var tl=g('<div class="user-input-field svelte-8tbe79"><!> <!> <!></div>'),sl=g('<div class="user-input-container svelte-8tbe79"><!> <div class="user-input-actions svelte-8tbe79"><!> <!></div></div>'),rl=g('<div slot="header-left" class="mcp-service-info svelte-8tbe79"><div class="mcp-service-title svelte-8tbe79"><!></div> <!> <!></div>'),al=g('<div class="installed-indicator svelte-8tbe79"><!></div>'),nl=g('<div slot="header-right" class="mcp-service-actions svelte-8tbe79"><!></div>'),il=g('<div class="mcp-service-item"><!></div>'),ol=g('<div class="mcp-install-content svelte-8tbe79"><div class="mcp-list-container svelte-8tbe79"></div></div>'),ll=g('<div slot="header-left" class="mcp-install-left svelte-8tbe79"><!> <!></div>'),dl=g('<div slot="header" class="mcp-install-header svelte-8tbe79"><!></div>'),cl=g('<div class="mcp-install-wrapper svelte-8tbe79"><!></div>');function ul(r,e){Xe(e,!1);let t=w(e,"onMCPServerAdd",24,()=>{}),s=w(e,"servers",24,()=>[]);const a=[{label:cr.REDIS,description:"Real-time data platform for building fast apps",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{label:cr.MONGODB,description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{label:cr.CIRCLECI,description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],l="easyMCPInstall.collapsed";let i=G(!1),o=G(!1),c=G(null),u=G({}),M=G({});function oe(A){var V;if(!A.userInput)return;for(let b=0;b<A.userInput.length;b++){const I=A.userInput[b];let j;if(j=I.type==="environmentVariable"&&I.envVarName?I.envVarName:I.correspondingArg?I.correspondingArg:`input_${b}`,!((V=n(u)[j])==null?void 0:V.trim())){const $=n(M)[j];return void($&&$.focus())}}let T=[A.command],v={};A.args&&T.push(...A.args);for(let b=0;b<A.userInput.length;b++){const I=A.userInput[b];let j;j=I.type==="environmentVariable"&&I.envVarName?I.envVarName:I.correspondingArg?I.correspondingArg:`input_${b}`;const N=n(u)[j].trim(),$=`"${N}"`;if(I.type==="environmentVariable"&&I.envVarName)v[I.envVarName]=N;else if(I.correspondingArg){const R=T.indexOf(I.correspondingArg);R!==-1?T.splice(R+1,0,$):T.push(I.correspondingArg,$)}else T.push($)}const J={type:"stdio",name:A.label,command:T.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys(v).length>0?v:void 0};t()&&t()(J),m(c,null),m(u,{})}function E(){m(c,null),m(u,{})}_e(()=>{},()=>{const A=localStorage.getItem(l);if(A!==null)try{m(i,JSON.parse(A))}catch{localStorage.removeItem(l)}m(o,!0)}),_e(()=>(n(o),n(i)),()=>{typeof window<"u"&&n(o)&&localStorage.setItem(l,JSON.stringify(n(i)))}),ct(),tt();var ce=cl(),ve=h(ce);ra(ve,{get collapsed(){return n(i)},set collapsed(A){m(i,A)},children:(A,T)=>{var v=ol(),J=h(v);dt(J,5,()=>a,qt,(V,b)=>{var I=il();Et(h(I),{$$slots:{"header-left":(j,N)=>{var $=rl(),R=h($),B=h(R);le(B,{size:1,weight:"medium",children:(P,x)=>{var ee=Z();ge(()=>Ae(ee,(n(b),y(()=>n(b).label)))),d(P,ee)},$$slots:{default:!0}});var L=p(R,2),f=P=>{le(P,{size:1,color:"secondary",children:(x,ee)=>{var Q=Z();ge(()=>Ae(Q,(n(b),y(()=>n(b).description)))),d(x,Q)},$$slots:{default:!0}})};W(L,P=>{n(b),y(()=>n(b).description)&&P(f)});var k=p(L,2),se=P=>{var x=sl(),ee=h(x);dt(ee,1,()=>(n(b),y(()=>n(b).userInput)),qt,(ze,Me,S)=>{var z=tl();const ye=$e(()=>(n(Me),y(()=>n(Me).type==="environmentVariable"&&n(Me).envVarName?n(Me).envVarName:n(Me).correspondingArg||`input_${S}`)));var we=h(z);le(we,{size:1,weight:"medium",color:"neutral",children:(Se,Ee)=>{var be=Z();ge(()=>Ae(be,(n(Me),y(()=>n(Me).label)))),d(Se,be)},$$slots:{default:!0}});var De=p(we,2),ne=Se=>{le(Se,{size:1,color:"secondary",children:(Ee,be)=>{var ue=Z();ge(()=>Ae(ue,(n(Me),y(()=>n(Me).description)))),d(Ee,ue)},$$slots:{default:!0}})};W(De,Se=>{n(Me),y(()=>n(Me).description)&&Se(ne)});var Ne=p(De,2);const Te=$e(()=>(n(Me),y(()=>n(Me).placeholder||"")));Xt(Ne,{get placeholder(){return n(Te)},size:1,variant:"surface",get value(){return n(u)[n(ye)]},set value(Se){lr(u,n(u)[n(ye)]=Se)},get textInput(){return n(M)[n(ye)]},set textInput(Se){lr(M,n(M)[n(ye)]=Se)},$$events:{keydown:Se=>{Se.key==="Enter"?oe(n(b)):Se.key==="Escape"&&E()}},$$legacy:!0}),d(ze,z)});var Q=p(ee,2),de=h(Q);Qe(de,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>oe(n(b))},children:(ze,Me)=>{var S=Z("Install");d(ze,S)},$$slots:{default:!0}});var me=p(de,2);Qe(me,{variant:"ghost-block",color:"neutral",size:1,$$events:{click:E},children:(ze,Me)=>{var S=Z("Cancel");d(ze,S)},$$slots:{default:!0}}),d(P,x)};W(k,P=>{n(c),n(b),y(()=>n(c)===n(b).label&&n(b).userInput)&&P(se)}),d(j,$)},"header-right":(j,N)=>{var $=nl(),R=h($),B=f=>{var k=al(),se=h(k);Mr.Root(se,{color:"success",size:1,variant:"soft",children:(P,x)=>{var ee=Z("Installed");d(P,ee)},$$slots:{default:!0}}),d(f,k)},L=(f,k)=>{var se=P=>{ts(P,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>function(x){if(s().some(Q=>Q.name===x.label))return;if(x.userInput&&x.userInput.length>0)return m(u,{}),x.userInput.forEach((Q,de)=>{let me;me=Q.type==="environmentVariable"&&Q.envVarName?Q.envVarName:Q.correspondingArg?Q.correspondingArg:`input_${de}`,lr(u,n(u)[me]=Q.defaultValue||"")}),void m(c,x.label);const ee={type:"stdio",name:x.label,command:x.command,arguments:"",useShellInterpolation:!0};t()&&t()(ee)}(n(b))},children:(x,ee)=>{hs(x,{})},$$slots:{default:!0}})};W(f,P=>{n(c),n(b),y(()=>n(c)!==n(b).label)&&P(se)},k)};W(R,f=>{_(s()),n(b),y(()=>s().some(k=>k.name===n(b).label))?f(B):f(L,!1)}),d(j,$)}}}),d(V,I)}),d(A,v)},$$slots:{default:!0,header:(A,T)=>{var v=dl();Et(h(v),{$$slots:{"header-left":(J,V)=>{var b=ll(),I=h(b);rn(I,{});var j=p(I,2);le(j,{color:"neutral",size:1,weight:"light",class:"card-title",children:(N,$)=>{var R=Z("Easy MCP Installation");d(N,R)},$$slots:{default:!0}}),d(J,b)}}}),d(A,v)}},$$legacy:!0}),d(r,ce),et()}const hl={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},pl={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},vl=Na(),ml=new class{constructor(r){Ie(this,"strings");let e={[dr.vscode]:{},[dr.jetbrains]:pl,[dr.web]:{}};this.strings={...hl,...e[r]}}get(r){return this.strings[r]}}(vl.clientType);var gl=g('<div class="section-heading-text">MCP</div>'),fl=g(`<div class="mcp-servers svelte-1vnq4q3"><div class="section-heading svelte-1vnq4q3"><!></div> <div class="description-text svelte-1vnq4q3">Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a>in the docs</a>.</div> <!> <!></div> <!> <div class="add-mcp-button-container svelte-1vnq4q3"><!> <!> <!></div>`,1),yl=g('<div class="section-heading-text">Terminal</div>'),_l=g("<!> <!>",1),wl=g('<div class="terminal-settings svelte-dndd5n"><!> <div class="shell-selector svelte-dndd5n"><!> <!></div> <div class="startup-script-container svelte-dndd5n"><!> <!></div></div>');function Cl(r,e){Xe(e,!1);const t=G();let s=w(e,"supportedShells",24,()=>[]),a=w(e,"selectedShell",24,()=>{}),l=w(e,"startupScript",28,()=>{}),i=w(e,"onShellSelect",8),o=w(e,"onStartupScriptChange",8),c=G();_e(()=>_(a()),()=>{var v;m(t,a()?(v=a(),s().find(J=>J.friendlyName===v)):void 0)}),ct(),tt();var u=wl(),M=h(u);le(M,{size:1,weight:"regular",color:"secondary",children:(v,J)=>{var V=yl();d(v,V)},$$slots:{default:!0}});var oe=p(M,2),E=h(oe);le(E,{size:1,children:(v,J)=>{var V=Z("Shell:");d(v,V)},$$slots:{default:!0}});var ce=p(E,2);Ye.Root(ce,{get requestClose(){return n(c)},set requestClose(v){m(c,v)},children:(v,J)=>{var V=_l(),b=Ce(V);Ye.Trigger(b,{children:(j,N)=>{const $=$e(()=>(_(s()),y(()=>s().length===0)));Qe(j,{size:1,variant:"outline",color:"neutral",get disabled(){return n($)},children:(R,B)=>{var L=it(),f=Ce(L),k=P=>{var x=Z();ge(()=>Ae(x,`${n(t),y(()=>n(t).friendlyName)??""}
            (${n(t),y(()=>n(t).supportString)??""})`)),d(P,x)},se=(P,x)=>{var ee=de=>{var me=Z("No shells available");d(de,me)},Q=de=>{var me=Z("Select a shell");d(de,me)};W(P,de=>{_(s()),y(()=>s().length===0)?de(ee):de(Q,!1)},x)};W(f,P=>{n(t),_(s()),y(()=>n(t)&&s().length>0)?P(k):P(se,!1)}),d(R,L)},$$slots:{default:!0,iconRight:(R,B)=>{Ua(R)}}})},$$slots:{default:!0}});var I=p(b,2);Ye.Content(I,{side:"bottom",align:"start",children:(j,N)=>{var $=it(),R=Ce($),B=f=>{var k=it(),se=Ce(k);dt(se,1,s,P=>P.friendlyName,(P,x)=>{const ee=$e(()=>(_(a()),n(x),y(()=>a()===n(x).friendlyName)));Ye.Item(P,{onSelect:()=>{i()(n(x).friendlyName),n(c)()},get highlight(){return n(ee)},children:(Q,de)=>{var me=Z();ge(()=>Ae(me,`${n(x),y(()=>n(x).friendlyName)??""}
              (${n(x),y(()=>n(x).supportString)??""})`)),d(Q,me)},$$slots:{default:!0}})}),d(f,k)},L=f=>{Ye.Label(f,{children:(k,se)=>{var P=Z("No shells available");d(k,P)},$$slots:{default:!0}})};W(R,f=>{_(s()),y(()=>s().length>0)?f(B):f(L,!1)}),d(j,$)},$$slots:{default:!0}}),d(v,V)},$$slots:{default:!0},$$legacy:!0});var ve=p(oe,2),A=h(ve);le(A,{size:1,children:(v,J)=>{var V=Z("Start-up script: Code to run wherever a new terminal is opened");d(v,V)},$$slots:{default:!0}});var T=p(A,2);na(T,{placeholder:"Enter shell commands to run on terminal startup",resize:"vertical",get value(){return l()},set value(v){l(v)},$$events:{change:function(v){const J=v.target;o()(J.value)}},$$legacy:!0}),d(r,u),et()}var bl=g('<div class="section-heading-text">Sound Settings</div>'),$l=g('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),Sl=g('<div slot="header-right"><!></div>'),kl=g('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),xl=g('<div slot="header-right"><!></div>'),Ml=g('<!> <div class="c-sound-settings svelte-8awonv"><!> <!></div>',1),Al=g('<div class="section-heading-text">Agent Settings</div>'),Tl=g('<div class="c-agent-setting__info svelte-mv39d5" slot="header-left"><div><!></div> <div><!></div> <div class="c-agent-setting__education svelte-mv39d5"><!></div></div>'),Nl=g('<div slot="header-right"><!></div>'),Rl=g('<!> <div class="c-agent-settings svelte-mv39d5"><!></div>',1),El=g('<div class="c-settings-tools svelte-181yusq"><!> <!> <!> <!> <!></div>');function Il(r,e){let t=w(e,"tools",24,()=>[]),s=w(e,"isMCPEnabled",8,!0),a=w(e,"isMCPImportEnabled",8,!0),l=w(e,"isTerminalEnabled",8,!0),i=w(e,"isSoundCategoryEnabled",8,!1),o=w(e,"isAgentCategoryEnabled",8,!1),c=w(e,"isSwarmModeFeatureFlagEnabled",8,!1),u=w(e,"hasEverUsedRemoteAgent",8,!1),M=w(e,"onAuthenticate",8),oe=w(e,"onRevokeAccess",8),E=w(e,"onToolApprovalConfigChange",8,()=>{}),ce=w(e,"onMCPServerAdd",8),ve=w(e,"onMCPServerSave",8),A=w(e,"onMCPServerDelete",8),T=w(e,"onMCPServerToggleDisable",8),v=w(e,"onMCPServerJSONImport",8),J=w(e,"onCancel",24,()=>{}),V=w(e,"supportedShells",24,()=>[]),b=w(e,"selectedShell",24,()=>{}),I=w(e,"startupScript",24,()=>{}),j=w(e,"onShellSelect",8,()=>{}),N=w(e,"onStartupScriptChange",8,()=>{});var $=El(),R=h($);Io(R,{title:"Services",get tools(){return t()},get onAuthenticate(){return M()},get onRevokeAccess(){return oe()},onToolApprovalConfigChange:E()});var B=p(R,2),L=Q=>{(function(de,me){Xe(me,!1);const[ze,Me]=Nt(),S=()=>st(ie,"$allServers",ze),z=G(),ye=G();let we=w(me,"onMCPServerAdd",8),De=w(me,"onMCPServerSave",8),ne=w(me,"onMCPServerDelete",8),Ne=w(me,"onMCPServerToggleDisable",8),Te=w(me,"onCancel",24,()=>{}),Se=w(me,"onMCPServerJSONImport",8),Ee=w(me,"isMCPImportEnabled",8,!0),be=G(null),ue=G(null);function qe(){var Ze;m(be,null),m(ue,null),(Ze=Te())==null||Ze()}let O=G([]);const U=ds(Ms.key),F=Nr(),X=U.getEnableNativeRemoteMcp(),ie=F.getServers();function te(Ze){m(be,Ze.id)}function he(Ze){return async function(...rt){const yt=await Ze(...rt);return m(ue,null),m(be,null),yt}}const D=he(we()),q=he(De()),re=he(Se()),Re=he(ne()),K=he(Ne()),Ve=ml.get("mcpDocsURL");_e(()=>(n(ue),n(be)),()=>{m(z,n(ue)==="add"||n(ue)==="addJson"||n(ue)==="addRemote"||n(be)!==null)}),_e(()=>S(),()=>{m(O,X?Va(S()):S())}),_e(()=>n(O),()=>{m(ye,Us.parseServerValidationMessages(n(O)))}),ct(),tt();var Le=fl(),Je=Ce(Le),Ue=h(Je),mt=h(Ue);le(mt,{size:1,weight:"regular",color:"secondary",children:(Ze,rt)=>{var yt=gl();d(Ze,yt)},$$slots:{default:!0}});var Oe=p(Ue,2),Fe=p(h(Oe)),at=p(Oe,2);ul(at,{get onMCPServerAdd(){return D},get servers(){return n(O)}});var We=p(at,2);dt(We,1,()=>n(O),Ze=>Ze.id,(Ze,rt)=>{const yt=$e(()=>(n(be),n(rt),y(()=>n(be)===n(rt).id?"edit":"view"))),Ot=$e(()=>(n(ye),n(rt),y(()=>n(ye).errors.get(n(rt).id)))),qs=$e(()=>(n(ye),n(rt),y(()=>n(ye).warnings.get(n(rt).id))));Wr(Ze,{get mode(){return n(yt)},get server(){return n(rt)},get onAdd(){return D},get onSave(){return q},get onDelete(){return Re},get onToggleDisableServer(){return K},onEdit:te,onCancel:qe,get onJSONImport(){return re},get disabledText(){return n(Ot)},get warningText(){return n(qs)}})});var He=p(Je,2),nt=Ze=>{Wr(Ze,{get mode(){return n(ue)},get onAdd(){return D},get onSave(){return q},get onDelete(){return Re},get onToggleDisableServer(){return K},onEdit:te,onCancel:qe,get onJSONImport(){return re}})};W(He,Ze=>{n(ue)!=="add"&&n(ue)!=="addJson"&&n(ue)!=="addRemote"||Ze(nt)});var St=p(He,2),ot=h(St);Qe(ot,{get disabled(){return n(z)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{m(ue,"add")}},children:(Ze,rt)=>{var yt=Z("Add MCP");d(Ze,yt)},$$slots:{default:!0,iconLeft:(Ze,rt)=>{hs(Ze,{slot:"iconLeft"})}}});var Lt=p(ot,2);Qe(Lt,{get disabled(){return n(z)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{m(ue,"addRemote")}},children:(Ze,rt)=>{var yt=Z("Add remote MCP");d(Ze,yt)},$$slots:{default:!0,iconLeft:(Ze,rt)=>{hs(Ze,{slot:"iconLeft"})}}});var Kt=p(Lt,2),fs=Ze=>{Qe(Ze,{get disabled(){return n(z)},color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$events:{click:()=>{m(ue,"addJson")}},children:(rt,yt)=>{var Ot=Z("Import from JSON");d(rt,Ot)},$$slots:{default:!0,iconLeft:(rt,yt)=>{ta(rt,{slot:"iconLeft"})}}})};W(Kt,Ze=>{Ee()&&Ze(fs)}),ge(()=>Qt(Fe,"href",Ve)),d(de,Le),et(),Me()})(Q,{get onMCPServerAdd(){return ce()},get onMCPServerSave(){return ve()},get onMCPServerDelete(){return A()},get onMCPServerToggleDisable(){return T()},get onMCPServerJSONImport(){return v()},get onCancel(){return J()},get isMCPImportEnabled(){return a()}})};W(B,Q=>{s()&&Q(L)});var f=p(B,2),k=Q=>{Cl(Q,{get supportedShells(){return V()},get selectedShell(){return b()},get startupScript(){return I()},onShellSelect:j(),onStartupScriptChange:N()})};W(f,Q=>{l()&&Q(k)});var se=p(f,2),P=Q=>{(function(de,me){Xe(me,!1);const[ze,Me]=Nt(),S=()=>st(n(z),"$currentSettings",ze),z=G(),ye=G(),we=ds(yr.key);async function De(){return await we.playAgentComplete(),"success"}_e(()=>{},()=>{ks(m(z,we.getCurrentSettings),"$currentSettings",ze)}),_e(()=>S(),()=>{m(ye,S().enabled)}),ct(),tt();var ne=Ml(),Ne=Ce(ne);le(Ne,{size:1,weight:"regular",color:"secondary",children:(ue,qe)=>{var O=bl();d(ue,O)},$$slots:{default:!0}});var Te=p(Ne,2),Se=h(Te);Et(Se,{$$slots:{"header-left":(ue,qe)=>{var O=$l(),U=h(O),F=h(U);le(F,{size:2,weight:"medium",children:(te,he)=>{var D=Z("Enable Sound Effects");d(te,D)},$$slots:{default:!0}});var X=p(U,2),ie=h(X);le(ie,{size:1,weight:"medium",children:(te,he)=>{var D=Z("Play a sound when an agent completes a task");d(te,D)},$$slots:{default:!0}}),d(ue,O)},"header-right":(ue,qe)=>{var O=Sl(),U=h(O);fr(U,{size:1,get checked(){return n(ye)},$$events:{change:()=>we.updateEnabled(!n(ye))}}),d(ue,O)}}});var Ee=p(Se,2),be=ue=>{Et(ue,{$$slots:{"header-left":(qe,O)=>{var U=kl(),F=h(U),X=h(F);le(X,{size:2,weight:"medium",children:(he,D)=>{var q=Z("Test Sound");d(he,q)},$$slots:{default:!0}});var ie=p(F,2),te=h(ie);le(te,{size:1,weight:"medium",children:(he,D)=>{var q=Z("Play a sample of the agent completion sound");d(he,q)},$$slots:{default:!0}}),d(qe,U)},"header-right":(qe,O)=>{var U=xl(),F=h(U);const X=$e(()=>n(ye)?"":"Enable sound effects to test"),ie=$e(()=>(_(ps),y(()=>[ps.Hover])));Ft(F,{get content(){return n(X)},get triggerOn(){return n(ie)},children:(te,he)=>{const D=$e(()=>!n(ye));dn(te,{size:1,defaultColor:"neutral",get enabled(){return n(ye)},stickyColor:!1,get disabled(){return n(D)},onClick:De,tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},children:(q,re)=>{var Re=Z("Play");d(q,Re)},$$slots:{default:!0}})},$$slots:{default:!0}}),d(qe,U)}}})};W(Ee,ue=>{n(ye)&&ue(be)}),d(de,ne),et(),Me()})(Q,{})};W(se,Q=>{i()&&Q(P)});var x=p(se,2),ee=Q=>{(function(de,me){Xe(me,!1);const[ze,Me]=Nt(),S=()=>st(n(z),"$currentSettings",ze),z=G(),ye=G();let we=w(me,"isSwarmModeEnabled",8,!1),De=w(me,"hasEverUsedRemoteAgent",8,!1);const ne=ds(js.key);_e(()=>{},()=>{ks(m(z,ne.getCurrentSettings),"$currentSettings",ze)}),_e(()=>S(),()=>{m(ye,S().enabled)}),ct(),tt();var Ne=it(),Te=Ce(Ne),Se=Ee=>{var be=Rl(),ue=Ce(be);le(ue,{size:1,weight:"regular",color:"secondary",children:(O,U)=>{var F=Al();d(O,F)},$$slots:{default:!0}});var qe=p(ue,2);Et(h(qe),{$$slots:{"header-left":(O,U)=>{var F=Tl(),X=h(F),ie=h(X);le(ie,{size:2,weight:"medium",children:(re,Re)=>{var K=Z("Enable Swarm Mode");d(re,K)},$$slots:{default:!0}});var te=p(X,2),he=h(te);le(he,{size:1,weight:"medium",children:(re,Re)=>{var K=Z("Allow agents to coordinate and work together on complex tasks");d(re,K)},$$slots:{default:!0}});var D=p(te,2),q=h(D);le(q,{size:1,weight:"regular",color:"secondary",children:(re,Re)=>{var K=Z(`Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.`);d(re,K)},$$slots:{default:!0}}),d(O,F)},"header-right":(O,U)=>{var F=Nl(),X=h(F);fr(X,{size:1,get checked(){return n(ye)},$$events:{change:()=>ne.updateEnabled(!n(ye))}}),d(O,F)}}}),d(Ee,be)};W(Te,Ee=>{we()&&De()&&Ee(Se)}),d(de,Ne),et(),Me()})(Q,{get isSwarmModeEnabled(){return c()},get hasEverUsedRemoteAgent(){return u()}})};W(x,Q=>{o()&&Q(ee)}),d(r,$)}var zl=$t("<svg><!></svg>");function Ll(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=zl();Ds(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 576 512",...t}));var a=h(s);nr(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',!0),d(r,s)}var Ol=$t("<svg><!></svg>");function Zl(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=Ol();Ds(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...t}));var a=h(s);nr(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',!0),d(r,s)}var Pl=g('<div class="c-user-guidelines-category__input svelte-10borzo"><!></div>');function fa(r,e){Xe(e,!1);const[t,s]=Nt(),a=xr();let l=w(e,"userGuidelines",12,""),i=w(e,"userGuidelinesLengthLimit",24,()=>{}),o=w(e,"updateUserGuideline",8,()=>!1);const c=ut(void 0);function u(){const E=l().trim();if(st(c,"$originalValue",t)!==E){if(!o()(E))throw i()&&E.length>i()?`The user guideline must be less than ${i()} character long`:"An error occurred updating the user";zr(c,E)}}ka(()=>{zr(c,l().trim())}),Kr(()=>{u()}),tt();var M=Pl(),oe=h(M);hn(oe,{placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:u,get value(){return l()},set value(E){l(E)},$$events:{focus:E=>{a("focus",E)}},$$legacy:!0}),d(r,M),et(),s()}var Fl=g("<!> <!> <!>",1),jl=g('<div slot="footer"><!> <!></div>'),Dl=g('<input type="text" value="No existing rules found" readonly="" class="c-dropdown-input svelte-z1s6x7"/>'),Vl=g('<div class="c-dropdown-trigger svelte-z1s6x7"><input type="text" readonly="" class="c-dropdown-input svelte-z1s6x7"/> <!></div>'),Ul=g("<!> <!>",1),ql=g("<!> <!>",1),Hl=g("<!> <!>",1),Bl=g("<!> <!> <!> <!>",1),Gl=g('<div slot="body" class="c-auto-import-rules-dialog svelte-z1s6x7"><!></div>'),Jl=g('<div slot="footer"><!> <!></div>');function Wl(r,e){Xe(e,!1);const[t,s]=Nt(),a=()=>st(n(ce),"$focusedIndex",t),l=G(),i=xr();let o=w(e,"show",8,!1),c=w(e,"options",24,()=>[]),u=w(e,"isLoading",8,!1),M=w(e,"errorMessage",8,""),oe=w(e,"successMessage",8,""),E=G(n(l)),ce=G(void 0),ve=G(()=>{});function A(){n(E)&&!u()&&i("select",n(E))}function T(){u()||(i("cancel"),m(E,n(l)))}_e(()=>_(c()),()=>{m(l,c().length>0?c()[0]:null)}),_e(()=>(_(o()),n(l)),()=>{o()&&m(E,n(l))}),ct(),tt(),ft("keydown",kr,function(v){o()&&!u()&&(v.key==="Escape"?(v.preventDefault(),T()):v.key==="Enter"&&n(E)&&(v.preventDefault(),A()))}),ia(r,{get show(){return o()},title:"Auto Import Rules",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return u()},get preventEscapeClose(){return u()},$$events:{cancel:T},$$slots:{body:(v,J)=>{var V=Gl(),b=h(V),I=N=>{var $=Dl();d(N,$)},j=N=>{var $=Bl(),R=Ce($);le(R,{size:2,color:"secondary",children:(x,ee)=>{var Q=Z("Select existing rules to auto import to .augment/rules");d(x,Q)},$$slots:{default:!0}});var B=p(R,2);const L=$e(()=>(_(c()),y(()=>c().length===0?[]:void 0)));Ye.Root(B,{get triggerOn(){return n(L)},get requestClose(){return n(ve)},set requestClose(x){m(ve,x)},get focusedIndex(){return n(ce)},set focusedIndex(x){ks(m(ce,x),"$focusedIndex",t)},children:(x,ee)=>{var Q=Hl(),de=Ce(Q);Ye.Trigger(de,{children:(ze,Me)=>{var S=Vl(),z=h(S),ye=p(z,2);Ar(ye,{class:"c-dropdown-chevron"}),ge(()=>xa(z,(n(E),y(()=>n(E)?n(E).label:"Existing rules")))),d(ze,S)},$$slots:{default:!0}});var me=p(de,2);Ye.Content(me,{align:"start",side:"bottom",children:(ze,Me)=>{var S=ql(),z=Ce(S);dt(z,1,c,qt,(De,ne)=>{const Ne=$e(()=>(n(E),n(ne),y(()=>{var Te;return((Te=n(E))==null?void 0:Te.label)===n(ne).label})));Ye.Item(De,{onSelect:()=>function(Te){m(E,Te),n(ve)()}(n(ne)),get highlight(){return n(Ne)},children:(Te,Se)=>{var Ee=Z();ge(()=>Ae(Ee,(n(ne),y(()=>n(ne).label)))),d(Te,Ee)},$$slots:{default:!0}})});var ye=p(z,2),we=De=>{var ne=Ul(),Ne=Ce(ne);Ye.Separator(Ne,{});var Te=p(Ne,2);Ye.Label(Te,{children:(Se,Ee)=>{var be=Z();ge(()=>Ae(be,(a(),_(c()),n(E),y(()=>{var ue;return a()!==void 0?c()[a()].description:(ue=n(E))==null?void 0:ue.description})))),d(Se,be)},$$slots:{default:!0}}),d(De,ne)};W(ye,De=>{(a()!==void 0||n(E))&&De(we)}),d(ze,S)},$$slots:{default:!0}}),d(x,Q)},$$slots:{default:!0},$$legacy:!0});var f=p(B,2),k=x=>{xs(x,{variant:"soft",color:"error",size:1,children:(ee,Q)=>{var de=Z();ge(()=>Ae(de,M())),d(ee,de)},$$slots:{default:!0,icon:(ee,Q)=>{Ws(ee,{slot:"icon"})}}})};W(f,x=>{M()&&x(k)});var se=p(f,2),P=x=>{xs(x,{variant:"soft",color:"success",size:1,children:(ee,Q)=>{var de=Z();ge(()=>Ae(de,oe())),d(ee,de)},$$slots:{default:!0,icon:(ee,Q)=>{qa(ee,{slot:"icon"})}}})};W(se,x=>{oe()&&x(P)}),d(N,$)};W(b,N=>{_(c()),y(()=>c().length===0)?N(I):N(j,!1)}),d(v,V)},footer:(v,J)=>{var V=Jl(),b=h(V);Qe(b,{variant:"solid",color:"neutral",get disabled(){return u()},$$events:{click:T},children:(N,$)=>{var R=Z("Cancel");d(N,R)},$$slots:{default:!0}});var I=p(b,2),j=N=>{const $=$e(()=>!n(E)||u());Qe(N,{color:"accent",variant:"solid",get disabled(){return n($)},get loading(){return u()},$$events:{click:A},children:(R,B)=>{var L=Z();ge(()=>Ae(L,u()?"Importing...":"Import ")),d(R,L)},$$slots:{default:!0}})};W(I,N=>{_(c()),y(()=>c().length>0)&&N(j)}),d(v,V)}}}),et(),s()}var Kl=g('<div class="loading-container"><!> <!></div>'),Yl=g('<div class="c-rules-list-empty svelte-mrq2l0"><!></div>'),Ql=g('<div class="c-rule-item-info svelte-mrq2l0" slot="header-left"><div class="l-icon-wrapper svelte-mrq2l0"><!></div> <div class="c-rule-item-path svelte-mrq2l0"><!></div></div>'),Xl=g('<div class="server-actions" slot="header-right"><div class="status-controls svelte-mrq2l0"><div class="c-rules-dropdown"><!></div> <!> <!></div></div>'),ed=g('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Create new rule file</div>'),td=g('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Import rules <!></div>'),sd=g("<!> <!>",1),rd=g("<!> <!>",1),ad=g("<!> <!>",1),nd=g(`<div class="c-rules-category svelte-mrq2l0"><div class="c-rules-section svelte-mrq2l0"><!> <div>Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!> <div class="c-rules-list svelte-mrq2l0"><!></div> <div class="c-rules-actions-container svelte-mrq2l0"><!> <!></div></div> <div class="c-user-guidelines-section svelte-mrq2l0"><!> <div>User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!></div></div> <!> <!>`,1);function id(r,e){Xe(e,!1);const[t,s]=Nt(),a=()=>st(J,"$rulesFiles",t),l=()=>st(n(o),"$isRulesLoading",t),i=()=>st(n(L),"$importFocusedIndex",t),o=G(),c=G(),u=G(),M=G();let oe=w(e,"userGuidelines",8,""),E=w(e,"userGuidelinesLengthLimit",24,()=>{}),ce=w(e,"workspaceGuidelinesLengthLimit",24,()=>{}),ve=w(e,"workspaceGuidelinesContent",8,""),A=w(e,"updateUserGuideline",8,()=>!1),T=w(e,"rulesModel",8),v=w(e,"rulesController",8);const J=T().getCachedRules(),V=v().getShowCreateRuleDialog(),b=v().getCreateRuleError();let I=G(!1),j=G([]),N=G(!1),$=G(""),R=G("");const B=[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}];let L=G(void 0),f=G(()=>{});async function k(O){try{O.id==="select_file_or_directory"?await v().selectFileToImport():O.id==="auto_import"&&await async function(){try{m($,""),m(R,"");const U=await v().getAutoImportOptions();m(j,U.data.options),m(I,!0)}catch(U){console.error("Failed to get auto-import options:",U),m($,"Failed to detect existing rules in workspace.")}}()}catch(U){console.error("Failed to handle import select:",U)}n(f)&&n(f)()}_e(()=>_(T()),()=>{ks(m(o,T().getLoading()),"$isRulesLoading",t)}),_e(()=>_(ce()),()=>{m(c,ce())}),_e(()=>(n(u),n(M),a(),_(ve()),n(c)),()=>{var O;O=en({rules:a(),workspaceGuidelinesContent:ve(),rulesAndGuidelinesLimit:n(c)}),m(u,O.isOverLimit),m(M,O.warningMessage)}),ct(),tt();var se=nd(),P=Ce(se),x=h(P),ee=h(x);le(ee,{class:"c-section-header",size:3,color:"primary",children:(O,U)=>{var F=Z("Rules");d(O,F)},$$slots:{default:!0}});var Q=p(ee,2),de=p(h(Q)),me=h(de);le(me,{size:1,weight:"regular",children:(O,U)=>{var F=Z("Learn more");d(O,F)},$$slots:{default:!0}});var ze=p(Q,2),Me=O=>{xs(O,{variant:"soft",color:"warning",size:1,children:(U,F)=>{var X=Z();ge(()=>Ae(X,n(M))),d(U,X)},$$slots:{default:!0,icon:(U,F)=>{Ws(U,{slot:"icon"})}}})};W(ze,O=>{n(u)&&O(Me)});var S=p(ze,2),z=h(S),ye=O=>{var U=Kl(),F=h(U);Ks(F,{size:1});var X=p(F,2);le(X,{size:1,color:"secondary",children:(ie,te)=>{var he=Z("Loading rules...");d(ie,he)},$$slots:{default:!0}}),d(O,U)},we=(O,U)=>{var F=ie=>{var te=Yl(),he=h(te);le(he,{size:1,color:"neutral",children:(D,q)=>{var re=Z("No rules files found");d(D,re)},$$slots:{default:!0}}),d(ie,te)},X=ie=>{var te=it(),he=Ce(te);dt(he,1,a,D=>D.path,(D,q)=>{Et(D,{isClickable:!0,$$events:{click:()=>v().openRule(n(q).path)},$$slots:{"header-left":(re,Re)=>{var K=Ql(),Ve=h(K),Le=h(Ve),Je=Fe=>{Ft(Fe,{content:"No description found",children:(at,We)=>{Ws(at,{})},$$slots:{default:!0}})},Ue=Fe=>{Ga(Fe,{})};W(Le,Fe=>{n(q),_(Or),y(()=>n(q).type===Or.AGENT_REQUESTED&&!n(q).description)?Fe(Je):Fe(Ue,!1)});var mt=p(Ve,2),Oe=h(mt);le(Oe,{size:1,color:"neutral",children:(Fe,at)=>{var We=Z();ge(()=>Ae(We,(n(q),y(()=>n(q).path)))),d(Fe,We)},$$slots:{default:!0}}),d(re,K)},"header-right":(re,Re)=>{var K=Xl(),Ve=h(K),Le=h(Ve),Je=h(Le);pn(Je,{get rule(){return n(q)},onSave:async(Oe,Fe)=>{await T().updateRuleContent({type:Oe,path:n(q).path,content:n(q).content,description:Fe})}});var Ue=p(Le,2);Qe(Ue,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Oe=>{Oe.stopPropagation(),v().openRule(n(q).path)}},$$slots:{iconRight:(Oe,Fe)=>{Ha(Oe,{slot:"iconRight"})}}});var mt=p(Ue,2);Qe(mt,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:Oe=>{Oe.stopPropagation(),v().deleteRule(n(q).path)}},$$slots:{iconRight:(Oe,Fe)=>{Ba(Oe,{slot:"iconRight"})}}}),d(re,K)}}})}),d(ie,te)};W(O,ie=>{a(),y(()=>a().length===0)?ie(F):ie(X,!1)},U)};W(z,O=>{l(),a(),y(()=>l()&&a().length===0)?O(ye):O(we,!1)});var De=p(S,2),ne=h(De);Qe(ne,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$events:{click:()=>v().createRule()},children:(O,U)=>{var F=ed(),X=h(F);hs(X,{slot:"iconLeft"}),d(O,F)},$$slots:{default:!0}});var Ne=p(ne,2);Ye.Root(Ne,{get requestClose(){return n(f)},set requestClose(O){m(f,O)},get focusedIndex(){return n(L)},set focusedIndex(O){ks(m(L,O),"$importFocusedIndex",t)},children:(O,U)=>{var F=ad(),X=Ce(F);Ye.Trigger(X,{children:(te,he)=>{Qe(te,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",children:(D,q)=>{var re=td(),Re=h(re);ta(Re,{slot:"iconLeft"});var K=p(Re,2);Ar(K,{slot:"iconRight"}),d(D,re)},$$slots:{default:!0}})},$$slots:{default:!0}});var ie=p(X,2);Ye.Content(ie,{align:"start",side:"bottom",children:(te,he)=>{var D=rd(),q=Ce(D);dt(q,1,()=>B,K=>K.id,(K,Ve)=>{Ye.Item(K,{onSelect:()=>k(n(Ve)),children:(Le,Je)=>{var Ue=Z();ge(()=>Ae(Ue,(n(Ve),y(()=>n(Ve).label)))),d(Le,Ue)},$$slots:{default:!0}})});var re=p(q,2),Re=K=>{var Ve=sd(),Le=Ce(Ve);Ye.Separator(Le,{});var Je=p(Le,2);Ye.Label(Je,{children:(Ue,mt)=>{var Oe=Z();ge(()=>Ae(Oe,(i(),y(()=>i()!==void 0?B[i()].description:B[0])))),d(Ue,Oe)},$$slots:{default:!0}}),d(K,Ve)};W(re,K=>{i()!==void 0&&K(Re)}),d(te,D)},$$slots:{default:!0}}),d(O,F)},$$slots:{default:!0},$$legacy:!0});var Te=p(x,2),Se=h(Te);le(Se,{class:"c-section-header",size:3,color:"primary",children:(O,U)=>{var F=Z("User Guidelines");d(O,F)},$$slots:{default:!0}});var Ee=p(Se,2),be=p(h(Ee)),ue=h(be);le(ue,{size:1,weight:"regular",children:(O,U)=>{var F=Z("Learn more");d(O,F)},$$slots:{default:!0}}),fa(p(Ee,2),{get userGuidelines(){return oe()},get userGuidelinesLengthLimit(){return E()},updateUserGuideline:A()});var qe=p(P,2);(function(O,U){Xe(U,!1);const F=xr();let X=w(U,"show",8,!1),ie=w(U,"errorMessage",8,""),te=G(""),he=G(void 0),D=G(!1);function q(){n(te).trim()&&!n(D)&&(m(D,!0),F("create",n(te).trim()))}function re(){n(D)||(F("cancel"),m(te,""))}function Re(K){n(D)||(K.key==="Enter"?(K.preventDefault(),q()):K.key==="Escape"&&(K.preventDefault(),re()))}_e(()=>(_(X()),n(he)),()=>{X()&&n(he)&&setTimeout(()=>{var K;return(K=n(he))==null?void 0:K.focus()},100)}),_e(()=>(_(X()),_(ie())),()=>{X()&&!ie()||m(D,!1)}),_e(()=>(_(X()),_(ie())),()=>{X()||ie()||m(te,"")}),ct(),tt(),ia(O,{get show(){return X()},title:"Create New Rule",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return n(D)},get preventEscapeClose(){return n(D)},$$events:{cancel:re,keydown:function(K){n(D)||K.detail.key==="Enter"&&(K.detail.preventDefault(),q())}},$$slots:{body:(K,Ve)=>{var Le=Fl(),Je=Ce(Le);le(Je,{size:2,color:"secondary",children:(Fe,at)=>{var We=Z("Enter a name for the new rule file (e.g., architecture.md):");d(Fe,We)},$$slots:{default:!0}});var Ue=p(Je,2);Xt(Ue,{placeholder:"rule-name.md",get disabled(){return n(D)},get value(){return n(te)},set value(Fe){m(te,Fe)},get textInput(){return n(he)},set textInput(Fe){m(he,Fe)},$$events:{keydown:Re},$$legacy:!0});var mt=p(Ue,2),Oe=Fe=>{xs(Fe,{variant:"soft",color:"error",size:1,children:(at,We)=>{var He=Z();ge(()=>Ae(He,ie())),d(at,He)},$$slots:{default:!0,icon:(at,We)=>{Ws(at,{slot:"icon"})}}})};W(mt,Fe=>{ie()&&Fe(Oe)}),d(K,Le)},footer:(K,Ve)=>{var Le=jl(),Je=h(Le);Qe(Je,{variant:"solid",color:"neutral",get disabled(){return n(D)},$$events:{click:re},children:(Oe,Fe)=>{var at=Z("Cancel");d(Oe,at)},$$slots:{default:!0}});var Ue=p(Je,2);const mt=$e(()=>(n(te),n(D),y(()=>!n(te).trim()||n(D))));Qe(Ue,{variant:"solid",color:"accent",get disabled(){return n(mt)},get loading(){return n(D)},$$events:{click:q},children:(Oe,Fe)=>{var at=Z();ge(()=>Ae(at,n(D)?"Creating...":"Create")),d(Oe,at)},$$slots:{default:!0}}),d(K,Le)}}}),et()})(qe,{get show(){return st(V,"$showCreateRuleDialog",t)},get errorMessage(){return st(b,"$createRuleError",t)},$$events:{create:function(O){v().handleCreateRuleWithName(O.detail)},cancel:function(){v().hideCreateRuleDialog()}}}),Wl(p(qe,2),{get show(){return n(I)},get options(){return n(j)},get isLoading(){return n(N)},get errorMessage(){return n($)},get successMessage(){return n(R)},$$events:{select:async function(O){const U=O.detail;try{m(N,!0),m($,"");const F=await v().processAutoImportSelection(U);let X=`Successfully imported ${F.importedRulesCount} rule${F.importedRulesCount!==1?"s":""} from ${U.label}`;F.duplicatesCount>0&&(X+=`, ${F.duplicatesCount} duplicate${F.duplicatesCount!==1?"s":""} skipped`),F.totalAttempted>F.importedRulesCount+F.duplicatesCount&&(X+=`, ${F.totalAttempted-F.importedRulesCount-F.duplicatesCount} failed`),m(R,X),setTimeout(()=>{m(I,!1),m(R,"")},500)}catch(F){console.error("Failed to process auto-import selection:",F),m($,"Failed to import rules. Please try again.")}finally{m(N,!1)}},cancel:function(){m(I,!1),m($,""),m(R,"")}}}),d(r,se),et(),s()}var od=$t("<svg><!></svg>");function ld(r,e){Xe(e,!1);let t=w(e,"onSignOut",8),s=G(!1);tt(),Qe(r,{get loading(){return n(s)},variant:"soft","data-testid":"sign-out-button",$$events:{click:function(){t()(),m(s,!0)}},children:(a,l)=>{var i=Z("Sign Out");d(a,i)},$$slots:{default:!0,iconLeft:(a,l)=>{(function(i,o){const c=us(o,["children","$$slots","$$events","$$legacy"]);var u=od();Ds(u,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...c}));var M=h(u);nr(M,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',!0),d(i,u)})(a,{slot:"iconLeft"})}}}),et()}class dd{constructor(e,t,s){Ie(this,"_showCreateRuleDialog",ut(!1));Ie(this,"_createRuleError",ut(""));Ie(this,"_extensionClient");this._host=e,this._msgBroker=t,this._rulesModel=s;const a=new Xr;this._extensionClient=new ea(e,t,a)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const t=await this._rulesModel.createRule(e.trim());t&&t.path&&await this.openRule(t.path),this._extensionClient.reportAgentSessionEvent({eventName:pr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Hs.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const s=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(s)}}else this.hideCreateRuleDialog()}async openRule(e){try{const t=await this._rulesModel.getWorkspaceRoot();e===Fr?this._extensionClient.openFile({repoRoot:t,pathName:Fr}):this._extensionClient.openFile({repoRoot:t,pathName:`${Wa}/${Ka}/${e}`})}catch(t){console.error("Failed to open rule:",t)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(t){console.error("Failed to delete rule:",t)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:Ge.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const t=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(t),this._reportSelectedImportMetrics(t)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const t=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(t),this._reportAutoImportMetrics(t),t}_showImportNotification(e){let t;e.importedRulesCount===0?t=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(t=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(t+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:t,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const t=e.directoryOrFile==="directory"?Hs.selectedDirectory:(e.directoryOrFile,Hs.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:pr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:pr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Hs.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}var cd=g('<span slot="content"><!></span>');function ud(r,e){Xe(e,!1);const[t,s]=Nt(),a=()=>st(P,"$guidelines",t),l=()=>st(N,"$settingsComponentSupported",t),i=()=>st($,"$enableAgentMode",t),o=()=>st(L,"$terminalSettingsStore",t),c=G(),u=G(),M=G(),oe=G(),E=G(),ce=new Ms(lt),ve=new Us(lt),A=new Mi(lt),T=new Ra(lt),v=new Xr,J=new ea(lt,T,v),V=new yr(T),b=new js(T),I=new Ya(T),j=new dd(lt,T,I);T.registerConsumer(I),_s(yr.key,V),_s(js.key,b),_s(Ms.key,ce),function(S){_s(ma,S)}(J),function(S){_s(ga,S)}(ve);const N=ce.getSettingsComponentSupported(),$=ce.getEnableAgentMode(),R=ce.getEnableAgentSwarmMode(),B=ce.getHasEverUsedRemoteAgent();T.registerConsumer(ce),T.registerConsumer(ve),T.registerConsumer(A);const L=A.getTerminalSettings();let f=G();const k={handleMessageFromExtension:S=>!(!S.data||S.data.type!==Ge.navigateToSettingsSection)&&(S.data.data&&typeof S.data.data=="string"&&m(f,S.data.data),!0)};T.registerConsumer(k);const se=ce.getDisplayableTools(),P=ce.getGuidelines();function x(S){const z=S.trim();return!(n(u)&&z.length>n(u))&&(ce.updateLocalUserGuidelines(z),lt.postMessage({type:Ge.updateUserGuidelines,data:S}),!0)}function ee(S){lt.postMessage({type:Ge.toolConfigStartOAuth,data:{authUrl:S}}),ce.startPolling()}async function Q(S){await J.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${S.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&lt.postMessage({type:Ge.toolConfigRevokeAccess,data:{toolId:S.identifier}})}function de(S){A.updateSelectedShell(S)}function me(S){A.updateStartupScript(S)}function ze(S,z){lt.postMessage({type:Ge.toolApprovalConfigSetRequest,data:{toolId:S,approvalConfig:z}})}function Me(){lt.postMessage({type:Ge.signOut})}Kr(()=>{ce.dispose(),V.dispose(),b.dispose()}),ce.notifyLoaded(),lt.postMessage({type:Ge.getOrientationStatus}),lt.postMessage({type:Ge.settingsPanelLoaded}),_e(()=>a(),()=>{var S;m(c,(S=a().userGuidelines)==null?void 0:S.contents)}),_e(()=>a(),()=>{var S;m(u,(S=a().userGuidelines)==null?void 0:S.lengthLimit)}),_e(()=>a(),()=>{var S,z;m(M,(z=(S=a().workspaceGuidelines)==null?void 0:S[0])==null?void 0:z.lengthLimit)}),_e(()=>a(),()=>{var S,z;m(oe,((z=(S=a().workspaceGuidelines)==null?void 0:S[0])==null?void 0:z.contents)||"")}),_e(()=>(l(),Pr),()=>{m(E,[l().remoteTools?Cs("Tools","",lo,"section-tools"):void 0,l().userGuidelines&&!l().rules?Cs("User Guidelines","Guidelines for Augment Chat to follow.",Ll,"guidelines"):void 0,l().rules?Cs("Rules and User Guidelines","",Zl,"guidelines"):void 0,l().workspaceContext?Cs("Context","",uo,"context"):void 0,Cs("Account","Manage your Augment account settings.",Pr,"account")].filter(Boolean))}),_e(()=>(n(E),n(f)),()=>{var S;n(E).length>1&&!n(f)&&m(f,(S=n(E)[0])==null?void 0:S.id)}),ct(),tt(),ft("message",kr,function(...S){var z;(z=T.onMessageFromExtension)==null||z.apply(this,S)}),un.Root(r,{children:(S,z)=>{io(S,{get items(){return n(E)},mode:"tree",class:"c-settings-navigation",get selectedId(){return n(f)},$$slots:{content:(ye,we)=>{var De=cd();const ne=$e(()=>we.item);var Ne=h(De),Te=Ee=>{},Se=(Ee,be)=>{var ue=O=>{qi(O,{})},qe=(O,U)=>{var F=ie=>{var te=it(),he=Ce(te),D=re=>{id(re,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},get workspaceGuidelinesLengthLimit(){return n(M)},get workspaceGuidelinesContent(){return n(oe)},updateUserGuideline:x,get rulesModel(){return I},get rulesController(){return j}})},q=re=>{fa(re,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},updateUserGuideline:x})};W(he,re=>{l(),y(()=>l().rules)?re(D):re(q,!1)}),d(ie,te)},X=(ie,te)=>{var he=q=>{ld(q,{onSignOut:Me})},D=q=>{const re=$e(()=>(i(),l(),y(()=>i()&&l().mcpServerList))),Re=$e(()=>(i(),l(),y(()=>i()&&l().mcpServerImport)));Il(q,{get tools(){return st(se,"$displayableTools",t)},onAuthenticate:ee,onRevokeAccess:Q,onToolApprovalConfigChange:ze,onMCPServerAdd:K=>ve.addServer(K),onMCPServerSave:K=>ve.updateServer(K),onMCPServerDelete:K=>ve.deleteServer(K),onMCPServerToggleDisable:K=>ve.toggleDisabledServer(K),onMCPServerJSONImport:K=>ve.importServersFromJSON(K),get isMCPEnabled(){return n(re)},get isMCPImportEnabled(){return n(Re)},get supportedShells(){return o(),y(()=>o().supportedShells)},get selectedShell(){return o(),y(()=>o().selectedShell)},get startupScript(){return o(),y(()=>o().startupScript)},onShellSelect:de,onStartupScriptChange:me,get isTerminalEnabled(){return l(),y(()=>l().terminal)},isSoundCategoryEnabled:!0,get isAgentCategoryEnabled(){return i()},get isSwarmModeFeatureFlagEnabled(){return st(R,"$enableAgentSwarmMode",t)},get hasEverUsedRemoteAgent(){return st(B,"$hasEverUsedRemoteAgent",t)}})};W(ie,q=>{_(n(ne)),y(()=>{var re;return((re=n(ne))==null?void 0:re.id)==="account"})?q(he):q(D,!1)},te)};W(O,ie=>{_(n(ne)),y(()=>{var te;return((te=n(ne))==null?void 0:te.id)==="guidelines"})?ie(F):ie(X,!1)},U)};W(Ee,O=>{_(n(ne)),y(()=>{var U;return((U=n(ne))==null?void 0:U.id)==="context"})?O(ue):O(qe,!1)},be)};W(Ne,Ee=>{_(Sr),_(n(ne)),y(()=>!Sr(n(ne)))?Ee(Te):Ee(Se,!1)}),d(ye,De)}}})},$$slots:{default:!0}}),et(),s()}(async function(){lt&&lt.initialize&&await lt.initialize(),Ma(ud,{target:document.getElementById("app")})})();
