import{i as _e,aB as ze,aC as Ce,c as Le,p as Se,n as Fe,U as Ee,l as E,f as Y,a as Z,t as y,b as z,v as He,u as Ie,z as Te,A as pe,B as I,w as ce,aD as Ae,_ as F,a0 as Be,D as fe,F as q,K as O,J as R,T as ie,M as re,a4 as X,ab as B,L as W,m as de,W as Me,V as G,S as Ue,N as me,a3 as ye,Q as ue,P as M,C as Q,I as he,G as De,a7 as Pe,H as Ke}from"./SpinnerAugment-B-W1rkU5.js";import{F as U}from"./focusTrapStack-wx6NNrdM.js";import{h as j,a as Ne,I as Oe}from"./IconButtonAugment-Cdot7Te3.js";import{b as qe}from"./CardAugment-6M90JowR.js";function as(s,e,o){var t,l=s,m=Ee,c=_e()?ze:Ce;Le(()=>{c(m,m=e())&&(t&&Se(t),t=Fe(()=>o(l)))})}function ve(s){return[...s.querySelectorAll("*")].filter(e=>e.tabIndex>=0)}const ns=(s,e={})=>{let{enabled:o=!0,initialFocus:t=null,restoreFocusOnClose:l=!0}=e,m=null,c=null,d=null;function h(){const i=ve(s);return[i[0]||s,i[i.length-1]||s]}function w(){if(!o)return;U.add(s),m=document.activeElement;const i=t||h()[0];i&&i.focus();const r=u=>{if(u.key!=="Tab"||!U.isActive(s))return;const a=document.activeElement;if(!s.contains(a))return;const[v,p]=h();u.shiftKey?a===v&&(u.preventDefault(),p.focus()):a===p&&(u.preventDefault(),v.focus())},x=u=>{if(!U.isActive(s))return;const a=u.target;if(s===a)return void(c==null?void 0:c.focus());if(s.contains(a))return void(c=a);if(ve(s).length===0)return;const[v]=h(),p=c||v;a!==p&&(p===s&&s.tabIndex<0||(document.body.contains(a)&&(c=p,p.focus()),a&&a!==document.body||(c=p,p.focus())))};document.addEventListener("keydown",r),document.addEventListener("focusin",x),d=()=>{document.removeEventListener("keydown",r),document.removeEventListener("focusin",x)}}function g(){U.remove(s),d&&(d(),d=null),l&&U.isEmpty()&&m&&typeof m.focus=="function"&&m.focus()}return o&&w(),{update(i={}){const r=o;o=i.enabled??o,t=i.initialFocus??t,l=i.restoreFocusOnClose??l,!r&&o?w():r&&!o&&g()},destroy(){g()}}};var Je=Y("<svg><!></svg>");function ls(s,e){const o=E(e,["children","$$slots","$$events","$$legacy"]);var t=Je();Z(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...o}));var l=y(t);j(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',!0),z(s,t)}var Ve=Y("<svg><!></svg>");function We(s,e){const o=E(e,["children","$$slots","$$events","$$legacy"]);var t=Ve();Z(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...o}));var l=y(t);j(l,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 498.7c-8.8 7-21.2 7-30 0l-160-128c-10.4-8.3-12-23.4-3.7-33.7s23.4-12 33.7-3.8l145 116 145-116c10.3-8.3 25.5-6.6 33.7 3.8s6.6 25.5-3.7 33.7zm160-357.4c10.4 8.3 12 23.4 3.8 33.7s-23.4 12-33.7 3.7L224 62.7l-145 116c-10.4 8.3-25.5 6.6-33.7-3.7s-6.6-25.5 3.7-33.7l160-128c8.8-7 21.2-7 30 0z"/>',!0),z(s,t)}const ge=Symbol("collapsible");function Ge(){return Ie(ge)}var Qe=q('<footer class="c-collapsible__footer svelte-zp1yo"><!></footer>'),Re=q("<div><!></div> <!>",1),Xe=q('<div><header><div data-collapsible-header="button" tabindex="0"><!></div></header> <div><div class="c-collapsible__content-inner svelte-zp1yo"><!></div></div></div>');function cs(s,e){const o=Te(e),t=E(e,["children","$$slots","$$events","$$legacy"]),l=E(t,["toggle","collapsed","stickyHeader","expandable","isHeaderStuck","toggleHeader","stickyHeaderTop"]);pe(e,!1);const[m,c]=ye(),d=()=>X(v,"$collapsedStore",m),h=()=>X($,"$expandableStore",m),w=de();let g=I(e,"collapsed",12,!1),i=I(e,"stickyHeader",8,!1),r=I(e,"expandable",12,!0),x=I(e,"isHeaderStuck",12,!1),u=I(e,"toggleHeader",8,!1),a=I(e,"stickyHeaderTop",24,()=>-.5);const v=ce(g()),p=Ae(v,f=>f),$=ce(r());let J,V=de(!1);function ee(f){r()?v.set(f):v.set(!0)}const D=function(){ee(!d())};He(ge,{collapsed:p,setCollapsed:ee,toggle:D,expandable:$}),F(()=>h(),()=>{r(h())}),F(()=>M(r()),()=>{$.set(r())}),F(()=>d(),()=>{g(d())}),F(()=>M(g()),()=>{v.set(g())}),F(()=>M(r()),()=>{r()||v.set(!0)}),F(()=>M(g()),()=>{g()?(clearTimeout(J),J=setTimeout(()=>{Q(V,!1)},200)):(clearTimeout(J),Q(V,!0))}),F(()=>(W(w),M(l)),()=>{Q(w,l.class)}),Be(),fe();var P=Xe();let se,te;var K=y(P);let oe;var H=y(K);let ae;var $e=y(H);O($e,e,"header",{},null),Ne(K,(f,b)=>function(k,_){const{onStuck:C,onUnstuck:T,offset:A=0}=_,n=document.createElement("div");n.style.position="absolute",n.style.top=A?`${A}px`:"0",n.style.height="1px",n.style.width="100%",n.style.pointerEvents="none",n.style.opacity="0",n.style.zIndex="-1";const L=k.parentNode;if(!L)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(L).position==="static"&&(L.style.position="relative"),L.insertBefore(n,k);const N=new IntersectionObserver(([S])=>{S.isIntersecting?T==null||T():C==null||C()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return N.observe(n),{update(S){_.onStuck=S.onStuck,_.onUnstuck=S.onUnstuck,S.offset!==void 0&&S.offset!==A&&(n.style.top=`${S.offset}px`)},destroy(){N.disconnect(),n.remove()}}}(f,b),()=>({offset:-a(),onStuck:()=>{x(!0)},onUnstuck:()=>{x(!1)}}));var ne=ue(K,2);let le;var be=y(ne),we=y(be),xe=f=>{var b=Re(),k=he(b);let _;var C=y(k);O(C,e,"default",{},null);var T=ue(k,2),A=n=>{var L=Qe(),N=y(L);O(N,e,"footer",{},null),z(n,L)};R(T,n=>{De(()=>o.footer)&&n(A)}),ie(n=>_=B(k,1,"c-collapsible__body svelte-zp1yo",null,_,n),[()=>({"c-collapsible__body--should-hide":!W(V)})],re),z(f,b)};R(we,f=>{h()&&f(xe)}),ie((f,b,k,_,C)=>{se=B(P,1,`c-collapsible ${W(w)??""}`,"svelte-zp1yo",se,f),te=Me(P,"",te,b),oe=B(K,1,"c-collapsible__header svelte-zp1yo",null,oe,k),ae=B(H,1,"c-collapsible__header-inner svelte-zp1yo",null,ae,_),G(H,"role",u()?"button":void 0),G(H,"aria-expanded",u()?!d():void 0),G(H,"aria-controls",u()?"collapsible-content":void 0),le=B(ne,1,"c-collapsible__content svelte-zp1yo",null,le,C)},[()=>({"is-collapsed":d(),"is-expandable":h()}),()=>({"--sticky-header-top":`${a()}px`}),()=>({"is-sticky":i()}),()=>({"is-collapsed":d(),"is-header-stuck":x(),"has-header-padding":a()>0}),()=>({"is-collapsed":d()})],re),Ue("click",H,function(...f){var b;(b=u()?D:void 0)==null||b.apply(this,f)}),z(s,P),qe(e,"toggle",D);var ke=me({toggle:D});return c(),ke}var Ye=Y("<svg><!></svg>");function Ze(s,e){const o=E(e,["children","$$slots","$$events","$$legacy"]);var t=Ye();Z(t,()=>({xmlns:"http://www.w3.org/2000/svg",width:"8",height:"10","data-ds-icon":"fa",viewBox:"0 0 8 10",...o}));var l=y(t);j(l,()=>'<path d="M4.451 6.357a.42.42 0 0 0-.527 0L1.11 8.607a.42.42 0 0 0-.065.592.424.424 0 0 0 .593.067l2.548-2.04 2.55 2.04a.423.423 0 0 0 .527-.66zm2.813-4.965a.422.422 0 1 0-.526-.658l-2.55 2.04-2.55-2.04a.421.421 0 1 0-.527.658l2.813 2.25a.42.42 0 0 0 .527 0z"/>',!0),z(s,t)}var je=q('<span class="c-collapse-button-augment__icon svelte-hw7s17"><!></span>');function is(s,e){const o=E(e,["children","$$slots","$$events","$$legacy"]),t=E(o,[]);pe(e,!1);const[l,m]=ye(),c=()=>X(d,"$collapsed",l),{collapsed:d,setCollapsed:h}=Ge();fe(),Oe(s,Pe({variant:"ghost-block",color:"neutral",size:1},()=>t,{$$events:{click:function(){h(!c())}},children:(w,g)=>{var i=je(),r=y(i);O(r,e,"default",{get collapsed(){return c()}},x=>{var u=Ke(),a=he(u),v=$=>{We($,{})},p=$=>{Ze($,{})};R(a,$=>{c()?$(v):$(p,!1)}),z(x,u)}),z(w,i)},$$slots:{default:!0}})),me(),m()}export{We as A,cs as C,ls as T,is as a,Ze as b,Ge as g,as as k,ns as t};
