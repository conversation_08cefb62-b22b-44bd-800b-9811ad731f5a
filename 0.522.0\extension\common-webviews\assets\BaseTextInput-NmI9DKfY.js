import{i as y,ai as h,G as b,aj as m,A as $,B as i,_ as k,a0 as z,D as B,F as S,a as A,L as C,a2 as E,m as j,X as D,H as F,I as G,K as H,b as p,t as I,N as K,C as L,a1 as o,P as M}from"./SpinnerAugment-B-W1rkU5.js";import"./IconButtonAugment-Cdot7Te3.js";function _(a,e,l=e){var r=y();h(a,"input",t=>{var s=t?a.defaultValue:a.value;if(s=n(a)?d(s):s,l(s),r&&s!==(s=e())){var u=a.selectionStart,c=a.selectionEnd;a.value=s??"",c!==null&&(a.selectionStart=u,a.selectionEnd=Math.min(c,a.value.length))}}),b(e)==null&&a.value&&l(n(a)?d(a.value):a.value),m(()=>{var t=e();n(a)&&t===d(a.value)||(a.type!=="date"||t||a.value)&&t!==a.value&&(a.value=t??"")})}function q(a,e,l=e){h(a,"change",r=>{var t=r?a.defaultChecked:a.checked;l(t)}),b(e)==null&&l(a.checked),m(()=>{var r=e();a.checked=!!r})}function n(a){var e=a.type;return e==="number"||e==="range"}function d(a){return a===""?null:+a}var N=S("<div><!></div>");function w(a,e){$(e,!1);const l=j();let r=i(e,"variant",8,"surface"),t=i(e,"size",8,2),s=i(e,"type",8,"default"),u=i(e,"color",24,()=>{});k(()=>(M(u()),o),()=>{L(l,u()?o(u()):o("accent"))}),z(),B();var c=N();A(c,v=>({...C(l),class:`c-base-text-input c-base-text-input--${r()} c-base-text-input--size-${t()}`,[E]:v}),[()=>({"c-base-text-input--has-color":u()!==void 0})],"svelte-1mx5zy6");var x=I(c);D(x,{get type(){return s()},get size(){return t()},children:(v,P)=>{var f=F(),g=G(f);H(g,e,"default",{},null),p(v,f)},$$slots:{default:!0}}),p(a,c),K()}export{w as B,q as a,_ as b};
