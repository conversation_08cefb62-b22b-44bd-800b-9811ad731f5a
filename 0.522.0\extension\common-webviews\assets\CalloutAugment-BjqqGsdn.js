import{z as N,l as C,A as P,B as s,_ as Q,C as m,m as q,$ as X,P as _,L as l,a0 as j,D as S,F as u,a as k,a1 as w,a2 as E,X as H,I as M,J as O,G as R,Q as T,K as b,t as r,b as v,N as U}from"./SpinnerAugment-B-W1rkU5.js";var V=u('<div class="c-callout-icon svelte-1u5qnh6"><!></div>'),W=u('<!> <div class="c-callout-body svelte-1u5qnh6"><!></div>',1),Y=u("<div><!></div>");function D(p,a){const y=N(a),x=C(a,["children","$$slots","$$events","$$legacy"]),t=C(x,["color","variant","size","highContrast"]);P(a,!1);const c=q(),o=q();let $=s(a,"color",8,"info"),A=s(a,"variant",8,"soft"),d=s(a,"size",8,2),B=s(a,"highContrast",8,!1);const F=d();Q(()=>(l(c),l(o),_(t)),()=>{m(c,t.class),m(o,X(t,["class"]))}),j(),S();var n=Y();k(n,(i,h)=>({...i,class:`c-callout c-callout--${$()} c-callout--${A()} c-callout--size-${d()} ${l(c)}`,...l(o),[E]:h}),[()=>w($()),()=>({"c-callout--highContrast":B()})],"svelte-1u5qnh6");var G=r(n);H(G,{get size(){return F},children:(i,h)=>{var f=W(),g=M(f),I=e=>{var z=V(),L=r(z);b(L,a,"icon",{},null),v(e,z)};O(g,e=>{R(()=>y.icon)&&e(I)});var J=T(g,2),K=r(J);b(K,a,"default",{},null),v(i,f)},$$slots:{default:!0}}),v(p,n),U()}export{D as C};
