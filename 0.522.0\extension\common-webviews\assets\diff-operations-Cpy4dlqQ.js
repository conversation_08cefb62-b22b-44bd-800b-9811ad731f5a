var Qt=Object.defineProperty;var Ye=u=>{throw TypeError(u)};var Wt=(u,e,t)=>e in u?Qt(u,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[e]=t;var v=(u,e,t)=>Wt(u,typeof e!="symbol"?e+"":e,t),Vt=(u,e,t)=>e.has(u)||Ye("Cannot "+t);var et=(u,e,t)=>e.has(u)?Ye("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(u):e.set(u,t);var me=(u,e,t)=>(Vt(u,e,"access private method"),t);import{A as B,B as g,_ as ue,P as d,C as ne,m as re,a0 as Fe,D as q,H as M,I as N,a as Qe,L,K as I,b as f,N as y,F as $,t as x,J as Pe,G as b,Y as zt,T as J,Z as de,M as se,ab as St,Q as we,V as ae,ak as Jt,S as tt,al as Ut,am as Tt,a3 as Xt,a4 as Gt,f as We,l as Kt,b0 as Yt}from"./SpinnerAugment-B-W1rkU5.js";import{e as ce,i as De,h as Rt,b as en}from"./IconButtonAugment-Cdot7Te3.js";import{c as tn}from"./svelte-component-BFVtr7-z.js";import{e as Lt}from"./Filespan-CTglRHBI.js";import{b as F}from"./CardAugment-6M90JowR.js";import"./toggleHighContrast-Cb9MCs64.js";import{a as nn,b as rn,S as un}from"./index-BPaFHx0a.js";function nt(...u){return"/"+u.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function rt(u){return u.startsWith("/")||u.startsWith("#")}function sn(u,e){B(e,!1);let t=g(e,"token",8),n=g(e,"options",8);const r=void 0;let s=re();ue(()=>(d(t()),d(n())),()=>{var l;ne(s,(l=t().text,n().slugger.slug(l).replace(/--+/g,"-")))}),Fe(),q();var o=M(),i=N(o);return Lt(i,()=>`h${t().depth}`,!1,(l,a)=>{Qe(l,()=>({id:L(s)}));var c=M(),D=N(c);I(D,e,"default",{},null),f(a,c)}),f(u,o),F(e,"renderers",r),y({renderers:r})}var on=$("<blockquote><!></blockquote>");function ln(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=on(),o=x(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}function be(u,e){let t=g(e,"tokens",8),n=g(e,"renderers",8),r=g(e,"options",8);var s=M(),o=N(s),i=l=>{var a=M(),c=N(a);ce(c,1,t,De,(D,h)=>{_t(D,{get token(){return L(h)},get renderers(){return n()},get options(){return r()}})}),f(l,a)};Pe(o,l=>{t()&&l(i)}),f(u,s)}function _t(u,e){B(e,!1);let t=g(e,"token",8),n=g(e,"renderers",8),r=g(e,"options",8);q();var s=M(),o=N(s),i=l=>{var a=M(),c=N(a);tn(c,()=>n()[t().type],(D,h)=>{h(D,{get token(){return t()},get options(){return r()},get renderers(){return n()},children:(p,k)=>{var E=M(),w=N(E),R=C=>{be(C,{get tokens(){return d(t()),b(()=>t().tokens)},get renderers(){return n()},get options(){return r()}})},O=C=>{var S=zt();J(()=>de(S,(d(t()),b(()=>t().raw)))),f(C,S)};Pe(w,C=>{d(t()),b(()=>"tokens"in t()&&t().tokens)?C(R):C(O,!1)}),f(p,E)},$$slots:{default:!0}})}),f(l,a)};Pe(o,l=>{d(n()),d(t()),b(()=>n()[t().type])&&l(i)}),f(u,s),y()}function an(u,e){B(e,!1);let t=g(e,"token",8),n=g(e,"options",8),r=g(e,"renderers",8),s=re();ue(()=>d(t()),()=>{ne(s,t().ordered?"ol":"ul")}),Fe(),q();var o=M(),i=N(o);Lt(i,()=>L(s),!1,(l,a)=>{Qe(l,()=>({start:(d(t()),b(()=>t().start||1))}));var c=M(),D=N(c);ce(D,1,()=>(d(t()),b(()=>t().items)),De,(h,p)=>{const k=se(()=>(L(p),b(()=>({...L(p)}))));_t(h,{get token(){return L(k)},get options(){return n()},get renderers(){return r()}})}),f(a,c)}),f(u,o),y()}var cn=$("<li><!></li>");function Dn(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=cn(),o=x(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var hn=$("<br/>");function pn(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=hn();return f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var Fn=$("<pre><code> </code></pre>");function dn(u,e){B(e,!1);let t=g(e,"token",8);const n=void 0,r=void 0;q();var s=Fn(),o=x(s),i=x(o);return J(()=>{St(o,1,(d(t()),b(()=>`lang-${t().lang}`))),de(i,(d(t()),b(()=>t().text)))}),f(u,s),F(e,"options",n),F(e,"renderers",r),y({options:n,renderers:r})}var fn=$("<code> </code>");function gn(u,e){B(e,!1);let t=g(e,"token",8);const n=void 0,r=void 0;q();var s=fn(),o=x(s);return J(i=>de(o,i),[()=>(d(t()),b(()=>t().raw.slice(1,t().raw.length-1)))],se),f(u,s),F(e,"options",n),F(e,"renderers",r),y({options:n,renderers:r})}var kn=$('<th scope="col"><!></th>'),Cn=$("<td><!></td>"),mn=$("<tr></tr>"),An=$("<table><thead><tr></tr></thead><tbody></tbody></table>");function En(u,e){B(e,!1);let t=g(e,"token",8),n=g(e,"options",8),r=g(e,"renderers",8);q();var s=An(),o=x(s),i=x(o);ce(i,5,()=>(d(t()),b(()=>t().header)),De,(a,c)=>{var D=kn();be(x(D),{get tokens(){return L(c),b(()=>L(c).tokens)},get options(){return n()},get renderers(){return r()}}),f(a,D)});var l=we(o);ce(l,5,()=>(d(t()),b(()=>t().rows)),De,(a,c)=>{var D=mn();ce(D,5,()=>L(c),De,(h,p)=>{var k=Cn();be(x(k),{get tokens(){return L(p),b(()=>L(p).tokens)},get options(){return n()},get renderers(){return r()}}),f(h,k)}),f(a,D)}),f(u,s),y()}function vn(u,e){B(e,!1);let t=g(e,"token",8);const n=void 0,r=void 0;q();var s=M(),o=N(s);return Rt(o,()=>(d(t()),b(()=>t().text))),f(u,s),F(e,"options",n),F(e,"renderers",r),y({options:n,renderers:r})}var xn=$("<p><!></p>");function wn(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=xn(),o=x(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var bn=$("<a><!></a>");function Bn(u,e){B(e,!1);let t=g(e,"token",8),n=g(e,"options",8);const r=void 0;q();var s=bn(),o=x(s);return I(o,e,"default",{},null),J(i=>{ae(s,"href",i),ae(s,"title",(d(t()),b(()=>t().title)))},[()=>(d(rt),d(t()),d(nt),d(n()),b(()=>rt(t().href)?nt(n().baseUrl,t().href):t().href))],se),f(u,s),F(e,"renderers",r),y({renderers:r})}function yn(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=M(),o=N(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var $n=$("<dfn><!></dfn>");function zn(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=$n(),o=x(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var Sn=$("<del><!></del>");function Tn(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=Sn(),o=x(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var Rn=$("<em><!></em>");function Ln(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=Rn(),o=x(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var _n=$("<hr/>");function In(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=_n();return f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var Pn=$("<strong><!></strong>");function On(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=Pn(),o=x(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}var jn=$('<img class="markdown-image svelte-z38cge"/>');function qn(u,e){B(e,!1);let t=g(e,"token",8);const n=void 0,r=void 0;q();var s=jn();return J(()=>{ae(s,"src",(d(t()),b(()=>t().href))),ae(s,"title",(d(t()),b(()=>t().title))),ae(s,"alt",(d(t()),b(()=>t().text)))}),f(u,s),F(e,"options",n),F(e,"renderers",r),y({options:n,renderers:r})}function ut(u,e){B(e,!1);const t=void 0,n=void 0,r=void 0;var s=M(),o=N(s);return I(o,e,"default",{},null),f(u,s),F(e,"token",t),F(e,"options",n),F(e,"renderers",r),y({token:t,options:n,renderers:r})}function Zn(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Y={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function st(u){Y=u}const It=/[&<>"']/,Mn=new RegExp(It.source,"g"),Pt=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Nn=new RegExp(Pt.source,"g"),Hn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ot=u=>Hn[u];function j(u,e){if(e){if(It.test(u))return u.replace(Mn,ot)}else if(Pt.test(u))return u.replace(Nn,ot);return u}const Qn=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Wn(u){return u.replace(Qn,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const Vn=/(^|[^\[])\^/g;function A(u,e){let t=typeof u=="string"?u:u.source;e=e||"";const n={replace:(r,s)=>{let o=typeof s=="string"?s:s.source;return o=o.replace(Vn,"$1"),t=t.replace(r,o),n},getRegex:()=>new RegExp(t,e)};return n}function it(u){try{u=encodeURI(u).replace(/%25/g,"%")}catch{return null}return u}const he={exec:()=>null};function lt(u,e){const t=u.replace(/\|/g,(r,s,o)=>{let i=!1,l=s;for(;--l>=0&&o[l]==="\\";)i=!i;return i?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function Ae(u,e,t){const n=u.length;if(n===0)return"";let r=0;for(;r<n&&u.charAt(n-r-1)===e;)r++;return u.slice(0,n-r)}function at(u,e,t,n){const r=e.href,s=e.title?j(e.title):null,o=u[1].replace(/\\([\[\]])/g,"$1");if(u[0].charAt(0)!=="!"){n.state.inLink=!0;const i={type:"link",raw:t,href:r,title:s,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,i}return{type:"image",raw:t,href:r,title:s,text:j(o)}}class Be{constructor(e){v(this,"options");v(this,"rules");v(this,"lexer");this.options=e||Y}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:Ae(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],r=function(s,o){const i=s.match(/^(\s+)(?:```)/);if(i===null)return o;const l=i[1];return o.split(`
`).map(a=>{const c=a.match(/^\s+/);if(c===null)return a;const[D]=c;return D.length>=l.length?a.slice(l.length):a}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const r=Ae(n,"#");this.options.pedantic?n=r.trim():r&&!/ $/.test(r)||(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=Ae(t[0].replace(/^ *>[ \t]?/gm,""),`
`),r=this.lexer.state.top;this.lexer.state.top=!0;const s=this.lexer.blockTokens(n);return this.lexer.state.top=r,{type:"blockquote",raw:t[0],tokens:s,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,s={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let i="",l="",a=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;i=t[0],e=e.substring(i.length);let D=t[2].split(`
`,1)[0].replace(/^\t+/,R=>" ".repeat(3*R.length)),h=e.split(`
`,1)[0],p=0;this.options.pedantic?(p=2,l=D.trimStart()):(p=t[2].search(/[^ ]/),p=p>4?1:p,l=D.slice(p),p+=t[1].length);let k=!1;if(!D&&/^ *$/.test(h)&&(i+=h+`
`,e=e.substring(h.length+1),c=!0),!c){const R=new RegExp(`^ {0,${Math.min(3,p-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),O=new RegExp(`^ {0,${Math.min(3,p-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),C=new RegExp(`^ {0,${Math.min(3,p-1)}}(?:\`\`\`|~~~)`),S=new RegExp(`^ {0,${Math.min(3,p-1)}}#`);for(;e;){const T=e.split(`
`,1)[0];if(h=T,this.options.pedantic&&(h=h.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),C.test(h)||S.test(h)||R.test(h)||O.test(e))break;if(h.search(/[^ ]/)>=p||!h.trim())l+=`
`+h.slice(p);else{if(k||D.search(/[^ ]/)>=4||C.test(D)||S.test(D)||O.test(D))break;l+=`
`+h}k||h.trim()||(k=!0),i+=T+`
`,e=e.substring(T.length+1),D=h.slice(p)}}s.loose||(a?s.loose=!0:/\n *\n *$/.test(i)&&(a=!0));let E,w=null;this.options.gfm&&(w=/^\[[ xX]\] /.exec(l),w&&(E=w[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),s.items.push({type:"list_item",raw:i,task:!!w,checked:E,loose:!1,text:l,tokens:[]}),s.raw+=i}s.items[s.items.length-1].raw=i.trimEnd(),s.items[s.items.length-1].text=l.trimEnd(),s.raw=s.raw.trimEnd();for(let c=0;c<s.items.length;c++)if(this.lexer.state.top=!1,s.items[c].tokens=this.lexer.blockTokens(s.items[c].text,[]),!s.loose){const D=s.items[c].tokens.filter(p=>p.type==="space"),h=D.length>0&&D.some(p=>/\n.*\n/.test(p.raw));s.loose=h}if(s.loose)for(let c=0;c<s.items.length;c++)s.items[c].loose=!0;return s}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),r=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:s}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=lt(t[1]),r=t[2].replace(/^\||\| *$/g,"").split("|"),s=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const i of r)/^ *-+: *$/.test(i)?o.align.push("right"):/^ *:-+: *$/.test(i)?o.align.push("center"):/^ *:-+ *$/.test(i)?o.align.push("left"):o.align.push(null);for(const i of n)o.header.push({text:i,tokens:this.lexer.inline(i)});for(const i of s)o.rows.push(lt(i,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:j(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=Ae(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(i,l){if(i.indexOf(l[1])===-1)return-1;let a=0;for(let c=0;c<i.length;c++)if(i[c]==="\\")c++;else if(i[c]===l[0])a++;else if(i[c]===l[1]&&(a--,a<0))return c;return-1}(t[2],"()");if(o>-1){const i=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,i).trim(),t[3]=""}}let r=t[2],s="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(r);o&&(r=o[1],s=o[3])}else s=t[3]?t[3].slice(1,-1):"";return r=r.trim(),/^</.test(r)&&(r=this.options.pedantic&&!/>$/.test(n)?r.slice(1):r.slice(1,-1)),at(t,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const r=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!r){const s=n[0].charAt(0);return{type:"text",raw:s,text:s}}return at(n,r,n[0],this.lexer)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(r&&!(r[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(r[1]||r[2])||!n||this.rules.inline.punctuation.exec(n))){const s=[...r[0]].length-1;let o,i,l=s,a=0;const c=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+s);(r=c.exec(t))!=null;){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(i=[...o].length,r[3]||r[4]){l+=i;continue}if((r[5]||r[6])&&s%3&&!((s+i)%3)){a+=i;continue}if(l-=i,l>0)continue;i=Math.min(i,i+l+a);const D=[...r[0]][0].length,h=e.slice(0,s+r.index+D+i);if(Math.min(s,i)%2){const k=h.slice(1,-1);return{type:"em",raw:h,text:k,tokens:this.lexer.inlineTokens(k)}}const p=h.slice(2,-2);return{type:"strong",raw:h,text:p,tokens:this.lexer.inlineTokens(p)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const r=/[^ ]/.test(n),s=/^ /.test(n)&&/ $/.test(n);return r&&s&&(n=n.substring(1,n.length-1)),n=j(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,r;return t[2]==="@"?(n=j(t[1]),r="mailto:"+n):(n=j(t[1]),r=n),{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let r,s;if(t[2]==="@")r=j(t[0]),s="mailto:"+r;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);r=j(t[0]),s=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:j(t[0]),{type:"text",raw:t[0],text:n}}}}const fe=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Ot=/(?:[*+-]|\d{1,9}[.)])/,jt=A(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Ot).getRegex(),Ve=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Je=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Jn=A(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Je).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Un=A(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Ot).getRegex(),Re="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Ue=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,Xn=A("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Ue).replace("tag",Re).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ct=A(Ve).replace("hr",fe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Re).getRegex(),Xe={blockquote:A(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ct).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Jn,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:fe,html:Xn,lheading:jt,list:Un,newline:/^(?: *(?:\n|$))+/,paragraph:ct,table:he,text:/^[^\n]+/},Dt=A("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",fe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Re).getRegex(),Gn={...Xe,table:Dt,paragraph:A(Ve).replace("hr",fe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Dt).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Re).getRegex()},Kn={...Xe,html:A(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ue).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:he,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:A(Ve).replace("hr",fe).replace("heading",` *#{1,6} *[^
]`).replace("lheading",jt).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},qt=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Zt=/^( {2,}|\\)\n(?!\s*$)/,ge="\\p{P}$+<=>`^|~",Yn=A(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,ge).getRegex(),er=A(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,ge).getRegex(),tr=A("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,ge).getRegex(),nr=A("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,ge).getRegex(),rr=A(/\\([punct])/,"gu").replace(/punct/g,ge).getRegex(),ur=A(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),sr=A(Ue).replace("(?:-->|$)","-->").getRegex(),or=A("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",sr).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),ye=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,ir=A(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",ye).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ht=A(/^!?\[(label)\]\[(ref)\]/).replace("label",ye).replace("ref",Je).getRegex(),pt=A(/^!?\[(ref)\](?:\[\])?/).replace("ref",Je).getRegex(),Ge={_backpedal:he,anyPunctuation:rr,autolink:ur,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:Zt,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:he,emStrongLDelim:er,emStrongRDelimAst:tr,emStrongRDelimUnd:nr,escape:qt,link:ir,nolink:pt,punctuation:Yn,reflink:ht,reflinkSearch:A("reflink|nolink(?!\\()","g").replace("reflink",ht).replace("nolink",pt).getRegex(),tag:or,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:he},lr={...Ge,link:A(/^!?\[(label)\]\((.*?)\)/).replace("label",ye).getRegex(),reflink:A(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ye).getRegex()},Oe={...Ge,escape:A(qt).replace("])","~|])").getRegex(),url:A(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},ar={...Oe,br:A(Zt).replace("{2,}","*").getRegex(),text:A(Oe.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Ee={normal:Xe,gfm:Gn,pedantic:Kn},oe={normal:Ge,gfm:Oe,breaks:ar,pedantic:lr};class Q{constructor(e){v(this,"tokens");v(this,"options");v(this,"state");v(this,"tokenizer");v(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Y,this.options.tokenizer=this.options.tokenizer||new Be,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:Ee.normal,inline:oe.normal};this.options.pedantic?(t.block=Ee.pedantic,t.inline=oe.pedantic):this.options.gfm&&(t.block=Ee.gfm,this.options.breaks?t.inline=oe.breaks:t.inline=oe.gfm),this.tokenizer.rules=t}static get rules(){return{block:Ee,inline:oe}}static lex(e,t){return new Q(t).lex(e)}static lexInline(e,t){return new Q(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,r,s,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(i,l,a)=>l+"    ".repeat(a.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(i=>!!(n=i.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),r=t[t.length-1],!r||r.type!=="paragraph"&&r.type!=="text"?t.push(n):(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),r=t[t.length-1],!r||r.type!=="paragraph"&&r.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(r.raw+=`
`+n.raw,r.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(s=e,this.options.extensions&&this.options.extensions.startBlock){let i=1/0;const l=e.slice(1);let a;this.options.extensions.startBlock.forEach(c=>{a=c.call({lexer:this},l),typeof a=="number"&&a>=0&&(i=Math.min(i,a))}),i<1/0&&i>=0&&(s=e.substring(0,i+1))}if(this.state.top&&(n=this.tokenizer.paragraph(s)))r=t[t.length-1],o&&r.type==="paragraph"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n),o=s.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&r.type==="text"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);else if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,r,s,o,i,l,a=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(a))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(a))!=null;)a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(a))!=null;)a=a.slice(0,o.index)+"++"+a.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(i||(l=""),i=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,a,l))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(s=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const D=e.slice(1);let h;this.options.extensions.startInline.forEach(p=>{h=p.call({lexer:this},D),typeof h=="number"&&h>=0&&(c=Math.min(c,h))}),c<1/0&&c>=0&&(s=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(s))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(l=n.raw.slice(-1)),i=!0,r=t[t.length-1],r&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class $e{constructor(e){v(this,"options");this.options=e||Y}code(e,t,n){var s;const r=(s=(t||"").match(/^\S*/))==null?void 0:s[0];return e=e.replace(/\n$/,"")+`
`,r?'<pre><code class="language-'+j(r)+'">'+(n?e:j(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:j(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const r=t?"ol":"ul";return"<"+r+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+r+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const r=it(e);if(r===null)return n;let s='<a href="'+(e=r)+'"';return t&&(s+=' title="'+t+'"'),s+=">"+n+"</a>",s}image(e,t,n){const r=it(e);if(r===null)return n;let s=`<img src="${e=r}" alt="${n}"`;return t&&(s+=` title="${t}"`),s+=">",s}text(e){return e}}class Ke{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class W{constructor(e){v(this,"options");v(this,"renderer");v(this,"textRenderer");this.options=e||Y,this.options.renderer=this.options.renderer||new $e,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Ke}static parse(e,t){return new W(t).parse(e)}static parseInline(e,t){return new W(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=s,i=this.options.extensions.renderers[o.type].call({parser:this},o);if(i!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=i||"";continue}}switch(s.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=s;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Wn(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=s;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=s;let i="",l="";for(let c=0;c<o.header.length;c++)l+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});i+=this.renderer.tablerow(l);let a="";for(let c=0;c<o.rows.length;c++){const D=o.rows[c];l="";for(let h=0;h<D.length;h++)l+=this.renderer.tablecell(this.parseInline(D[h].tokens),{header:!1,align:o.align[h]});a+=this.renderer.tablerow(l)}n+=this.renderer.table(i,a);continue}case"blockquote":{const o=s,i=this.parse(o.tokens);n+=this.renderer.blockquote(i);continue}case"list":{const o=s,i=o.ordered,l=o.start,a=o.loose;let c="";for(let D=0;D<o.items.length;D++){const h=o.items[D],p=h.checked,k=h.task;let E="";if(h.task){const w=this.renderer.checkbox(!!p);a?h.tokens.length>0&&h.tokens[0].type==="paragraph"?(h.tokens[0].text=w+" "+h.tokens[0].text,h.tokens[0].tokens&&h.tokens[0].tokens.length>0&&h.tokens[0].tokens[0].type==="text"&&(h.tokens[0].tokens[0].text=w+" "+h.tokens[0].tokens[0].text)):h.tokens.unshift({type:"text",text:w+" "}):E+=w+" "}E+=this.parse(h.tokens,a),c+=this.renderer.listitem(E,k,!!p)}n+=this.renderer.list(c,i,l);continue}case"html":{const o=s;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=s;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=s,i=o.tokens?this.parseInline(o.tokens):o.text;for(;r+1<e.length&&e[r+1].type==="text";)o=e[++r],i+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(i):i;continue}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=this.options.extensions.renderers[s.type].call({parser:this},s);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){n+=o||"";continue}}switch(s.type){case"escape":{const o=s;n+=t.text(o.text);break}case"html":{const o=s;n+=t.html(o.text);break}case"link":{const o=s;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=s;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=s;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=s;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=s;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=s;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=s;n+=t.text(o.text);break}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class pe{constructor(e){v(this,"options");this.options=e||Y}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}v(pe,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var K,je,Mt,$t;const G=new($t=class{constructor(...u){et(this,K);v(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});v(this,"options",this.setOptions);v(this,"parse",me(this,K,je).call(this,Q.lex,W.parse));v(this,"parseInline",me(this,K,je).call(this,Q.lexInline,W.parseInline));v(this,"Parser",W);v(this,"Renderer",$e);v(this,"TextRenderer",Ke);v(this,"Lexer",Q);v(this,"Tokenizer",Be);v(this,"Hooks",pe);this.use(...u)}walkTokens(u,e){var n,r;let t=[];for(const s of u)switch(t=t.concat(e.call(this,s)),s.type){case"table":{const o=s;for(const i of o.header)t=t.concat(this.walkTokens(i.tokens,e));for(const i of o.rows)for(const l of i)t=t.concat(this.walkTokens(l.tokens,e));break}case"list":{const o=s;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=s;(r=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&r[o.type]?this.defaults.extensions.childTokens[o.type].forEach(i=>{const l=o[i].flat(1/0);t=t.concat(this.walkTokens(l,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...u){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return u.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if("renderer"in r){const s=e.renderers[r.name];e.renderers[r.name]=s?function(...o){let i=r.renderer.apply(this,o);return i===!1&&(i=s.apply(this,o)),i}:r.renderer}if("tokenizer"in r){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const s=e[r.level];s?s.unshift(r.tokenizer):e[r.level]=[r.tokenizer],r.start&&(r.level==="block"?e.startBlock?e.startBlock.push(r.start):e.startBlock=[r.start]:r.level==="inline"&&(e.startInline?e.startInline.push(r.start):e.startInline=[r.start]))}"childTokens"in r&&r.childTokens&&(e.childTokens[r.name]=r.childTokens)}),n.extensions=e),t.renderer){const r=this.defaults.renderer||new $e(this.defaults);for(const s in t.renderer){if(!(s in r))throw new Error(`renderer '${s}' does not exist`);if(s==="options")continue;const o=s,i=t.renderer[o],l=r[o];r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c||""}}n.renderer=r}if(t.tokenizer){const r=this.defaults.tokenizer||new Be(this.defaults);for(const s in t.tokenizer){if(!(s in r))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const o=s,i=t.tokenizer[o],l=r[o];r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c}}n.tokenizer=r}if(t.hooks){const r=this.defaults.hooks||new pe;for(const s in t.hooks){if(!(s in r))throw new Error(`hook '${s}' does not exist`);if(s==="options")continue;const o=s,i=t.hooks[o],l=r[o];pe.passThroughHooks.has(s)?r[o]=a=>{if(this.defaults.async)return Promise.resolve(i.call(r,a)).then(D=>l.call(r,D));const c=i.call(r,a);return l.call(r,c)}:r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c}}n.hooks=r}if(t.walkTokens){const r=this.defaults.walkTokens,s=t.walkTokens;n.walkTokens=function(o){let i=[];return i.push(s.call(this,o)),r&&(i=i.concat(r.call(this,o))),i}}this.defaults={...this.defaults,...n}}),this}setOptions(u){return this.defaults={...this.defaults,...u},this}lexer(u,e){return Q.lex(u,e??this.defaults)}parser(u,e){return W.parse(u,e??this.defaults)}},K=new WeakSet,je=function(u,e){return(t,n)=>{const r={...n},s={...this.defaults,...r};this.defaults.async===!0&&r.async===!1&&(s.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),s.async=!0);const o=me(this,K,Mt).call(this,!!s.silent,!!s.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(s.hooks&&(s.hooks.options=s),s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then(i=>u(i,s)).then(i=>s.hooks?s.hooks.processAllTokens(i):i).then(i=>s.walkTokens?Promise.all(this.walkTokens(i,s.walkTokens)).then(()=>i):i).then(i=>e(i,s)).then(i=>s.hooks?s.hooks.postprocess(i):i).catch(o);try{s.hooks&&(t=s.hooks.preprocess(t));let i=u(t,s);s.hooks&&(i=s.hooks.processAllTokens(i)),s.walkTokens&&this.walkTokens(i,s.walkTokens);let l=e(i,s);return s.hooks&&(l=s.hooks.postprocess(l)),l}catch(i){return o(i)}}},Mt=function(u,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,u){const n="<p>An error occurred:</p><pre>"+j(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},$t);function m(u,e){return G.parse(u,e)}m.options=m.setOptions=function(u){return G.setOptions(u),m.defaults=G.defaults,st(m.defaults),m},m.getDefaults=Zn,m.defaults=Y,m.use=function(...u){return G.use(...u),m.defaults=G.defaults,st(m.defaults),m},m.walkTokens=function(u,e){return G.walkTokens(u,e)},m.parseInline=G.parseInline,m.Parser=W,m.parser=W.parse,m.Renderer=$e,m.TextRenderer=Ke,m.Lexer=Q,m.lexer=Q.lex,m.Tokenizer=Be,m.Hooks=pe,m.parse=m,m.options,m.setOptions,m.use,m.walkTokens,m.parseInline,W.parse,Q.lex;const cr=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,Dr=Object.hasOwnProperty;class hr{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let r=function(o,i){return typeof o!="string"?"":(i||(o=o.toLowerCase()),o.replace(cr,"").replace(/ /g,"-"))}(e,t===!0);const s=r;for(;Dr.call(n.occurrences,r);)n.occurrences[s]++,r=s+"-"+n.occurrences[s];return n.occurrences[r]=0,r}reset(){this.occurrences=Object.create(null)}}const pr=()=>({heading:sn,blockquote:ln,list:an,list_item:Dn,br:pn,code:dn,codespan:gn,table:En,html:vn,paragraph:wn,link:Bn,text:yn,def:zn,del:Tn,em:Ln,hr:In,strong:On,image:qn,space:ut,escape:ut}),Fr=()=>({baseUrl:"/",slugger:new hr});function dr(u,e){B(e,!1),function(){const l=console.warn;console.warn=a=>{a.includes("unknown prop")||a.includes("unexpected slot")||l(a)},Jt(()=>{console.warn=l})}();let t=g(e,"source",8),n=g(e,"options",24,()=>({})),r=g(e,"renderers",24,()=>({})),s=re(),o=re(),i=re();ue(()=>(d(t()),d(r()),d(n())),()=>{var l;ne(s,(l=t(),new Q().lex(l))),ne(o,{...pr(),...r()}),ne(i,{...Fr(),...n()})}),Fe(),q(),be(u,{get tokens(){return L(s)},get renderers(){return L(o)},get options(){return L(i)}}),y()}var fr=$('<div class="c-codeblock svelte-19vw87l" role="button" tabindex="0"><div class="c-codeblock__top-bar-anchor monaco-component svelte-19vw87l"><div class="c-codeblock__top-bar-left svelte-19vw87l"><!></div> <!></div> <!> <div class="c-codeblock__actions-bar-anchor svelte-19vw87l"><!></div></div>');function Ft(u,e){B(e,!1);const[t,n]=Xt();let r=g(e,"scroll",8,!1),s=g(e,"token",8),o=g(e,"language",24,()=>{}),i=g(e,"padding",24,()=>({top:0,bottom:0})),l=g(e,"editorInstance",28,()=>{}),a=g(e,"element",28,()=>{}),c=g(e,"height",24,()=>{});const D=nn.getContext().monaco,h=rn(),p=()=>l()&&(k()||E())||"",k=()=>{var ee;if(!l())return;const z=l().getSelections();if(!(z!=null&&z.length))return;const Z=l().getModel();if(z.map(X=>(Z==null?void 0:Z.getValueLengthInRange(X))||0).reduce((X,te)=>X+te,0)!==0)return z.sort((ee=Gt(D,"$monaco",t))==null?void 0:ee.Range.compareRangesUsingStarts).map(X=>(Z==null?void 0:Z.getValueInRange(X))||"").join(`
`)},E=()=>{if(l())return l().getValue()||""};ue(()=>d(i()),()=>{var Z;var z;z=i(),(Z=l())==null||Z.updateOptions({padding:z})}),ue(()=>(d(l()),d(c())),()=>{var z;(z=l())==null||z.updateOptions({scrollbar:{vertical:c()!==void 0?"auto":"hidden"}})}),Fe(),q();var w=fr();tt("focus",Ut,()=>h.requestLayout());var R=x(w),O=x(R),C=x(O);I(C,e,"topBarLeft",{},null);var S=we(O,2);I(S,e,"topBarRight",{},null);var T=we(R,2);const H=se(()=>({lineNumbers:"off",wrappingIndent:"same",padding:i(),wordWrap:r()?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"})),P=se(()=>(d(o()),d(s()),b(()=>o()||s().lang)));un(T,{get options(){return L(H)},get text(){return d(s()),b(()=>s().text)},get lang(){return L(P)},get height(){return c()},get editorInstance(){return l()},set editorInstance(z){l(z)},$$legacy:!0});var _=we(T,2),U=x(_);I(U,e,"actionsBar",{},z=>{}),Tt(w,z=>a(z),()=>a()),tt("mouseenter",w,function(z){en.call(this,e,z)}),f(u,w),F(e,"getSelectionOrContents",p),F(e,"getSelections",k),F(e,"getContents",E);var ke=y({getSelectionOrContents:p,getSelections:k,getContents:E});return n(),ke}var gr=$('<span><code class="markdown-codespan svelte-1dofrdh"><!></code></span>');function dt(u,e){B(e,!1);const t=re();let n=g(e,"token",8),r=g(e,"element",28,()=>{});ue(()=>d(n()),()=>{ne(t,n().raw.slice(1,n().raw.length-1))}),Fe(),q();var s=gr(),o=x(s),i=x(o);I(i,e,"default",{get codespanContents(){return L(t)}},l=>{var a=zt();J(()=>de(a,L(t))),f(l,a)}),Tt(s,l=>r(l),()=>r()),f(u,s),y()}var kr=$("<span> </span>");function ft(u,e){B(e,!1);let t=g(e,"token",8);q();var n=kr(),r=x(n);J(()=>de(r,`~${d(t()),b(()=>t().text)??""}~`)),f(u,n),y()}var Cr=$('<p class="augment-markdown-paragraph svelte-1edcdk9"><!></p>');function gt(u,e){var t=Cr(),n=x(t);I(n,e,"default",{},null),f(u,t)}var mr=$('<div class="c-markdown svelte-vgp0rq"><!></div>');function Or(u,e){let t=g(e,"markdown",8),n=g(e,"renderers",24,()=>({}));var r=mr(),s=x(r);const o=se(()=>(d(dt),d(Ft),d(gt),d(ft),d(n()),b(()=>({codespan:dt,code:Ft,paragraph:gt,del:ft,...n()}))));dr(s,{get source(){return t()},get renderers(){return L(o)}}),f(u,r)}var Ar=We('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.26047 5.79388C5.07301 5.98134 5.07301 6.28525 5.26047 6.47271C5.44792 6.66015 5.75184 6.66015 5.93929 6.47271L7.99988 4.41211L10.0605 6.47271C10.2479 6.66015 10.5518 6.66015 10.7393 6.47271C10.9267 6.28525 10.9267 5.98134 10.7393 5.79388L8.33929 3.39388C8.24926 3.30387 8.12717 3.2533 7.99988 3.2533C7.87257 3.2533 7.75048 3.30387 7.66046 3.39388L5.26047 5.79388ZM10.7393 10.206C10.9267 10.0186 10.9267 9.71467 10.7393 9.52722C10.5518 9.33977 10.2479 9.33977 10.0605 9.52722L7.99988 11.5878L5.93929 9.52722C5.75184 9.33977 5.44792 9.33977 5.26047 9.52722C5.07301 9.71467 5.07301 10.0186 5.26047 10.206L7.66046 12.6061C7.84792 12.7935 8.15184 12.7935 8.33929 12.6061L10.7393 10.206Z" fill="currentColor" fill-opacity="1"></path></svg>');function jr(u){var e=Ar();f(u,e)}var Er=We("<svg><!></svg>");function qr(u,e){const t=Kt(e,["children","$$slots","$$events","$$legacy"]);var n=Er();Qe(n,()=>({xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16","data-ds-icon":"fa",viewBox:"0 1 16 16",...t}));var r=x(n);Rt(r,()=>'<path fill-rule="evenodd" d="M4.049 3.252a.53.53 0 0 1 .524-.015l9.6 5.067a.533.533 0 0 1 0 .943l-9.6 5.067a.533.533 0 0 1-.782-.472V3.71c0-.187.098-.36.258-.457" clip-rule="evenodd"/>',!0),f(u,n)}var vr=We('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738" fill="currentColor"></path></svg>');function Zr(u,e){let t=g(e,"class",8,"");var n=vr();J(()=>St(n,0,Yt(t()))),f(u,n)}function V(){}function kt(u,e,t,n,r){for(var s,o=[];e;)o.push(e),s=e.previousComponent,delete e.previousComponent,e=s;o.reverse();for(var i=0,l=o.length,a=0,c=0;i<l;i++){var D=o[i];if(D.removed)D.value=u.join(n.slice(c,c+D.count)),c+=D.count;else{if(!D.added&&r){var h=t.slice(a,a+D.count);h=h.map(function(p,k){var E=n[c+k];return E.length>p.length?E:p}),D.value=u.join(h)}else D.value=u.join(t.slice(a,a+D.count));a+=D.count,D.added||(c+=D.count)}}return o}function Ct(u,e){var t;for(t=0;t<u.length&&t<e.length;t++)if(u[t]!=e[t])return u.slice(0,t);return u.slice(0,t)}function mt(u,e){var t;if(!u||!e||u[u.length-1]!=e[e.length-1])return"";for(t=0;t<u.length&&t<e.length;t++)if(u[u.length-(t+1)]!=e[e.length-(t+1)])return u.slice(-t);return u.slice(-t)}function qe(u,e,t){if(u.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(u)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return t+u.slice(e.length)}function Ze(u,e,t){if(!e)return u+t;if(u.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(u)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return u.slice(0,-e.length)+t}function ie(u,e){return qe(u,e,"")}function ve(u,e){return Ze(u,e,"")}function At(u,e){return e.slice(0,function(t,n){var r=0;t.length>n.length&&(r=t.length-n.length);var s=n.length;t.length<n.length&&(s=t.length);var o=Array(s),i=0;o[0]=0;for(var l=1;l<s;l++){for(n[l]==n[i]?o[l]=o[i]:o[l]=i;i>0&&n[l]!=n[i];)i=o[i];n[l]==n[i]&&i++}i=0;for(var a=r;a<t.length;a++){for(;i>0&&t[a]!=n[i];)i=o[i];t[a]==n[i]&&i++}return i}(u,e))}V.prototype={diff:function(u,e){var t,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=n.callback;typeof n=="function"&&(r=n,n={});var s=this;function o(C){return C=s.postProcess(C,n),r?(setTimeout(function(){r(C)},0),!0):C}u=this.castInput(u,n),e=this.castInput(e,n),u=this.removeEmpty(this.tokenize(u,n));var i=(e=this.removeEmpty(this.tokenize(e,n))).length,l=u.length,a=1,c=i+l;n.maxEditLength!=null&&(c=Math.min(c,n.maxEditLength));var D=(t=n.timeout)!==null&&t!==void 0?t:1/0,h=Date.now()+D,p=[{oldPos:-1,lastComponent:void 0}],k=this.extractCommon(p[0],e,u,0,n);if(p[0].oldPos+1>=l&&k+1>=i)return o(kt(s,p[0].lastComponent,e,u,s.useLongestToken));var E=-1/0,w=1/0;function R(){for(var C=Math.max(E,-a);C<=Math.min(w,a);C+=2){var S=void 0,T=p[C-1],H=p[C+1];T&&(p[C-1]=void 0);var P=!1;if(H){var _=H.oldPos-C;P=H&&0<=_&&_<i}var U=T&&T.oldPos+1<l;if(P||U){if(S=!U||P&&T.oldPos<H.oldPos?s.addToPath(H,!0,!1,0,n):s.addToPath(T,!1,!0,1,n),k=s.extractCommon(S,e,u,C,n),S.oldPos+1>=l&&k+1>=i)return o(kt(s,S.lastComponent,e,u,s.useLongestToken));p[C]=S,S.oldPos+1>=l&&(w=Math.min(w,C-1)),k+1>=i&&(E=Math.max(E,C+1))}else p[C]=void 0}a++}if(r)(function C(){setTimeout(function(){if(a>c||Date.now()>h)return r();R()||C()},0)})();else for(;a<=c&&Date.now()<=h;){var O=R();if(O)return O}},addToPath:function(u,e,t,n,r){var s=u.lastComponent;return s&&!r.oneChangePerToken&&s.added===e&&s.removed===t?{oldPos:u.oldPos+n,lastComponent:{count:s.count+1,added:e,removed:t,previousComponent:s.previousComponent}}:{oldPos:u.oldPos+n,lastComponent:{count:1,added:e,removed:t,previousComponent:s}}},extractCommon:function(u,e,t,n,r){for(var s=e.length,o=t.length,i=u.oldPos,l=i-n,a=0;l+1<s&&i+1<o&&this.equals(t[i+1],e[l+1],r);)l++,i++,a++,r.oneChangePerToken&&(u.lastComponent={count:1,previousComponent:u.lastComponent,added:!1,removed:!1});return a&&!r.oneChangePerToken&&(u.lastComponent={count:a,previousComponent:u.lastComponent,added:!1,removed:!1}),u.oldPos=i,l},equals:function(u,e,t){return t.comparator?t.comparator(u,e):u===e||t.ignoreCase&&u.toLowerCase()===e.toLowerCase()},removeEmpty:function(u){for(var e=[],t=0;t<u.length;t++)u[t]&&e.push(u[t]);return e},castInput:function(u){return u},tokenize:function(u){return Array.from(u)},join:function(u){return u.join("")},postProcess:function(u){return u}};var ze="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",xr=new RegExp("[".concat(ze,"]+|\\s+|[^").concat(ze,"]"),"ug"),xe=new V;function Et(u,e,t,n){if(e&&t){var r=e.value.match(/^\s*/)[0],s=e.value.match(/\s*$/)[0],o=t.value.match(/^\s*/)[0],i=t.value.match(/\s*$/)[0];if(u){var l=Ct(r,o);u.value=Ze(u.value,o,l),e.value=ie(e.value,l),t.value=ie(t.value,l)}if(n){var a=mt(s,i);n.value=qe(n.value,i,a),e.value=ve(e.value,a),t.value=ve(t.value,a)}}else if(t)u&&(t.value=t.value.replace(/^\s*/,"")),n&&(n.value=n.value.replace(/^\s*/,""));else if(u&&n){var c=n.value.match(/^\s*/)[0],D=e.value.match(/^\s*/)[0],h=e.value.match(/\s*$/)[0],p=Ct(c,D);e.value=ie(e.value,p);var k=mt(ie(c,p),h);e.value=ve(e.value,k),n.value=qe(n.value,c,k),u.value=Ze(u.value,c,c.slice(0,c.length-k.length))}else if(n){var E=n.value.match(/^\s*/)[0],w=At(e.value.match(/\s*$/)[0],E);e.value=ve(e.value,w)}else if(u){var R=At(u.value.match(/\s*$/)[0],e.value.match(/^\s*/)[0]);e.value=ie(e.value,R)}}xe.equals=function(u,e,t){return t.ignoreCase&&(u=u.toLowerCase(),e=e.toLowerCase()),u.trim()===e.trim()},xe.tokenize=function(u){var e,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t.intlSegmenter){if(t.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');e=Array.from(t.intlSegmenter.segment(u),function(s){return s.segment})}else e=u.match(xr)||[];var n=[],r=null;return e.forEach(function(s){/\s/.test(s)?r==null?n.push(s):n.push(n.pop()+s):/\s/.test(r)?n[n.length-1]==r?n.push(n.pop()+s):n.push(r+s):n.push(s),r=s}),n},xe.join=function(u){return u.map(function(e,t){return t==0?e:e.replace(/^\s+/,"")}).join("")},xe.postProcess=function(u,e){if(!u||e.oneChangePerToken)return u;var t=null,n=null,r=null;return u.forEach(function(s){s.added?n=s:s.removed?r=s:((n||r)&&Et(t,r,n,s),t=s,n=null,r=null)}),(n||r)&&Et(t,r,n,null),u},new V().tokenize=function(u){var e=new RegExp("(\\r?\\n)|[".concat(ze,"]+|[^\\S\\n\\r]+|[^").concat(ze,"]"),"ug");return u.match(e)||[]};var Se=new V;function vt(u,e,t){return Se.diff(u,e,t)}function xt(u,e){var t=Object.keys(u);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(u);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(u,r).enumerable})),t.push.apply(t,n)}return t}function Te(u){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?xt(Object(t),!0).forEach(function(n){br(u,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(t)):xt(Object(t)).forEach(function(n){Object.defineProperty(u,n,Object.getOwnPropertyDescriptor(t,n))})}return u}function wr(u){var e=function(t,n){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var s=r.call(t,n);if(typeof s!="object")return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(t)}(u,"string");return typeof e=="symbol"?e:e+""}function Me(u){return Me=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Me(u)}function br(u,e,t){return(e=wr(e))in u?Object.defineProperty(u,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):u[e]=t,u}function Le(u){return function(e){if(Array.isArray(e))return _e(e)}(u)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(u)||function(e,t){if(e){if(typeof e=="string")return _e(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return _e(e,t)}}(u)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function _e(u,e){(e==null||e>u.length)&&(e=u.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=u[t];return n}Se.tokenize=function(u,e){e.stripTrailingCr&&(u=u.replace(/\r\n/g,`
`));var t=[],n=u.split(/(\n|\r\n)/);n[n.length-1]||n.pop();for(var r=0;r<n.length;r++){var s=n[r];r%2&&!e.newlineIsToken?t[t.length-1]+=s:t.push(s)}return t},Se.equals=function(u,e,t){return t.ignoreWhitespace?(t.newlineIsToken&&u.includes(`
`)||(u=u.trim()),t.newlineIsToken&&e.includes(`
`)||(e=e.trim())):t.ignoreNewlineAtEof&&!t.newlineIsToken&&(u.endsWith(`
`)&&(u=u.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),V.prototype.equals.call(this,u,e,t)},new V().tokenize=function(u){return u.split(/(\S.+?[.!?])(?=\s+|$)/)},new V().tokenize=function(u){return u.split(/([{}:;,]|\s+)/)};var le=new V;function Ne(u,e,t,n,r){var s,o;for(e=e||[],t=t||[],n&&(u=n(r,u)),s=0;s<e.length;s+=1)if(e[s]===u)return t[s];if(Object.prototype.toString.call(u)==="[object Array]"){for(e.push(u),o=new Array(u.length),t.push(o),s=0;s<u.length;s+=1)o[s]=Ne(u[s],e,t,n,r);return e.pop(),t.pop(),o}if(u&&u.toJSON&&(u=u.toJSON()),Me(u)==="object"&&u!==null){e.push(u),o={},t.push(o);var i,l=[];for(i in u)Object.prototype.hasOwnProperty.call(u,i)&&l.push(i);for(l.sort(),s=0;s<l.length;s+=1)o[i=l[s]]=Ne(u[i],e,t,n,i);e.pop(),t.pop()}else o=u;return o}le.useLongestToken=!0,le.tokenize=Se.tokenize,le.castInput=function(u,e){var t=e.undefinedReplacement,n=e.stringifyReplacer,r=n===void 0?function(s,o){return o===void 0?t:o}:n;return typeof u=="string"?u:JSON.stringify(Ne(u,null,null,r),r,"  ")},le.equals=function(u,e,t){return V.prototype.equals.call(le,u.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),t)};var Ie=new V;function Mr(u){var e=u.split(/\n/),t=[],n=0;function r(){var i={};for(t.push(i);n<e.length;){var l=e[n];if(/^(\-\-\-|\+\+\+|@@)\s/.test(l))break;var a=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(l);a&&(i.index=a[1]),n++}for(s(i),s(i),i.hunks=[];n<e.length;){var c=e[n];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(c))break;if(/^@@/.test(c))i.hunks.push(o());else{if(c)throw new Error("Unknown line "+(n+1)+" "+JSON.stringify(c));n++}}}function s(i){var l=/^(---|\+\+\+)\s+(.*)\r?$/.exec(e[n]);if(l){var a=l[1]==="---"?"old":"new",c=l[2].split("	",2),D=c[0].replace(/\\\\/g,"\\");/^".*"$/.test(D)&&(D=D.substr(1,D.length-2)),i[a+"FileName"]=D,i[a+"Header"]=(c[1]||"").trim(),n++}}function o(){var i=n,l=e[n++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),a={oldStart:+l[1],oldLines:l[2]===void 0?1:+l[2],newStart:+l[3],newLines:l[4]===void 0?1:+l[4],lines:[]};a.oldLines===0&&(a.oldStart+=1),a.newLines===0&&(a.newStart+=1);for(var c=0,D=0;n<e.length&&(D<a.oldLines||c<a.newLines||(h=e[n])!==null&&h!==void 0&&h.startsWith("\\"));n++){var h,p=e[n].length==0&&n!=e.length-1?" ":e[n][0];if(p!=="+"&&p!=="-"&&p!==" "&&p!=="\\")throw new Error("Hunk at line ".concat(i+1," contained invalid line ").concat(e[n]));a.lines.push(e[n]),p==="+"?c++:p==="-"?D++:p===" "&&(c++,D++)}if(c||a.newLines!==1||(a.newLines=0),D||a.oldLines!==1||(a.oldLines=0),c!==a.newLines)throw new Error("Added line count did not match for hunk at line "+(i+1));if(D!==a.oldLines)throw new Error("Removed line count did not match for hunk at line "+(i+1));return a}for(;n<e.length;)r();return t}function wt(u,e,t,n,r,s,o){if(o||(o={}),typeof o=="function"&&(o={callback:o}),o.context===void 0&&(o.context=4),o.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(!o.callback)return l(vt(t,n,o));var i=o.callback;function l(a){if(a){a.push({value:"",lines:[]});for(var c=[],D=0,h=0,p=[],k=1,E=1,w=function(){var P=a[R],_=P.lines||function(te){var Nt=te.endsWith(`
`),Ce=te.split(`
`).map(function(Ht){return Ht+`
`});return Nt?Ce.pop():Ce.push(Ce.pop().slice(0,-1)),Ce}(P.value);if(P.lines=_,P.added||P.removed){var U;if(!D){var ke=a[R-1];D=k,h=E,ke&&(p=o.context>0?H(ke.lines.slice(-o.context)):[],D-=p.length,h-=p.length)}(U=p).push.apply(U,Le(_.map(function(te){return(P.added?"+":"-")+te}))),P.added?E+=_.length:k+=_.length}else{if(D)if(_.length<=2*o.context&&R<a.length-2){var z;(z=p).push.apply(z,Le(H(_)))}else{var Z,ee=Math.min(_.length,o.context);(Z=p).push.apply(Z,Le(H(_.slice(0,ee))));var X={oldStart:D,oldLines:k-D+ee,newStart:h,newLines:E-h+ee,lines:p};c.push(X),D=0,h=0,p=[]}k+=_.length,E+=_.length}},R=0;R<a.length;R++)w();for(var O=0,C=c;O<C.length;O++)for(var S=C[O],T=0;T<S.lines.length;T++)S.lines[T].endsWith(`
`)?S.lines[T]=S.lines[T].slice(0,-1):(S.lines.splice(T+1,0,"\\ No newline at end of file"),T++);return{oldFileName:u,newFileName:e,oldHeader:r,newHeader:s,hunks:c}}function H(P){return P.map(function(_){return" "+_})}}vt(t,n,Te(Te({},o),{},{callback:function(a){var c=l(a);i(c)}}))}function He(u){if(Array.isArray(u))return u.map(He).join(`
`);var e=[];u.oldFileName==u.newFileName&&e.push("Index: "+u.oldFileName),e.push("==================================================================="),e.push("--- "+u.oldFileName+(u.oldHeader===void 0?"":"	"+u.oldHeader)),e.push("+++ "+u.newFileName+(u.newHeader===void 0?"":"	"+u.newHeader));for(var t=0;t<u.hunks.length;t++){var n=u.hunks[t];n.oldLines===0&&(n.oldStart-=1),n.newLines===0&&(n.newStart-=1),e.push("@@ -"+n.oldStart+","+n.oldLines+" +"+n.newStart+","+n.newLines+" @@"),e.push.apply(e,n.lines)}return e.join(`
`)+`
`}function Br(u,e,t,n,r,s,o){var i;if(typeof o=="function"&&(o={callback:o}),(i=o)===null||i===void 0||!i.callback){var l=wt(u,e,t,n,r,s,o);return l?He(l):void 0}var a=o.callback;wt(u,e,t,n,r,s,Te(Te({},o),{},{callback:function(c){c?a(He(c)):a()}}))}function bt(u){let e=0;const t=1e4,n=u.length>t?u.substring(0,5e3)+u.substring(u.length-5e3):u;for(let r=0;r<n.length;r++)e=(e<<5)-e+n.charCodeAt(r),e|=0;return Math.abs(e).toString(36)}function Bt(u,e,t,n,r={}){const{context:s=3,generateId:o=!0}=r,i=Br(u,e,t,n,"","",{context:s}),l=e||u;let a;return o?a=`${bt(l)}-${bt(t+n)}`:a=Math.random().toString(36).substring(2,15),{id:a,path:l,diff:i,originalCode:t,modifiedCode:n}}function yt(u){const e=u.split(`
`);return{additions:e.filter(t=>t.startsWith("+")&&!t.startsWith("+++")).length,deletions:e.filter(t=>t.startsWith("-")&&!t.startsWith("---")).length}}function yr(u){return!u.originalCode||u.originalCode.trim()===""}function $r(u){return!u.modifiedCode||u.modifiedCode.trim()===""}Ie.tokenize=function(u){return u.slice()},Ie.join=Ie.removeEmpty=function(u){return u};const Nr=100,Hr="Too many files changed to display in the diff view. Please review the files in the remote workspace directly to inspect changes.";class Qr{static generateDiff(e,t,n,r){return Bt(e,t,n,r)}static generateDiffs(e){return function(t,n={}){return t.map(r=>Bt(r.oldPath,r.newPath,r.oldContent,r.newContent,n))}(e)}static getDiffStats(e){return yt(e)}static getDiffObjectStats(e){return yt(e.diff)}static isNewFile(e){return yr(e)}static isDeletedFile(e){return $r(e)}}export{dt as C,Qr as D,jr as E,Or as M,Zr as O,qr as P,yt as a,$r as b,Br as c,Ft as d,Nr as e,Hr as f,bt as g,yr as i,Mr as p};
